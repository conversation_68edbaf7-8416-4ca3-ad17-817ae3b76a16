﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace 视觉检测系统
{
    public class CalcUtils
    {
        private static Mat tpl;
        private readonly static IniFileUtils iniFileUtil = new IniFileUtils(Environment.CurrentDirectory + "\\AppConfig.ini");
        public static bool CalcProcess(Mat roi, Rect boundingRect)
        {
            // 二值化
            Mat binaryImage = new Mat();
            double thresholdValue = 128; // 阈值，可以根据需要调整
            double maxValue = 255;       // 二值化后的最大值
            Cv2.Threshold(roi, binaryImage, thresholdValue, maxValue, ThresholdTypes.BinaryInv);

            // 腐蚀操作
            Mat binaryImage1 = new Mat();
            Cv2.Erode(binaryImage, binaryImage1, null, new Point(-1, -1), 11); // 使用null作为核

            // 形态学开操作
            Mat se = Cv2.GetStructuringElement(MorphShapes.Rect, new Size(13, 13), new Point(-1, -1));
            Cv2.MorphologyEx(binaryImage1, binaryImage1, MorphTypes.Open, se);


            //Cv2.ImShow("se", se);
            //Cv2.WaitKey(0);
            // 将ROI区域内的boundingRect填充为0
            Cv2.Rectangle(binaryImage1, boundingRect.TopLeft, boundingRect.BottomRight, Scalar.Black, -1);

            //Cv2.ImShow("test", binaryImage1);
            //Cv2.WaitKey(0);
            // 查找轮廓
            Point[][] contours;
            HierarchyIndex[] hierarchy;
            Cv2.FindContours(binaryImage1, out contours, out hierarchy, RetrievalModes.External, ContourApproximationModes.ApproxSimple);

            if (contours.Length < 2 )
            {
                return false;
            }

            // 检查轮廓面积
            bool flag = true;
            int cut = 0;
            foreach (var contour in contours)
            {
                double area = Cv2.ContourArea(contour);

                
                if (area > GetData())
                {
                    cut++;
                }
            }
            if (cut >= 2)
            {
                // return true;
                flag = true;
            }
            else
            {
                flag = false;
            }
            return flag;
        }
        private static Point2f[] GetPoints()
        {
            string[] points = new string[4];
            for (int i = 0; i < 4; i++)
            {
                points[i] = iniFileUtil.ReadString("roi", $"point{i + 1}", "");
            }

            Point2f[] result = new Point2f[4];
            for (int i = 0; i < 4; i++)
            {
                if (!string.IsNullOrEmpty(points[i]))
                {
                    // 移除 "point" 和括号，然后按逗号分割
                    string[] coords = points[i].Replace("(", "").Replace(")", "").Split(',');
                    if (coords.Length == 2)
                    {
                        float x = float.Parse(coords[0].Trim());
                        float y = float.Parse(coords[1].Trim());
                        result[i] = new Point2f(x, y);
                    }
                    else
                    {
                        MessageBox.Show($"点{i + 1}的坐标格式错误");
                        return null;
                    }
                }
                else
                {
                    MessageBox.Show($"点{i + 1}的坐标未配置");
                    return null;
                }
            }

            return result;
        }
        private static int GetData()
        {
            int.TryParse(iniFileUtil.ReadString("data", "data", ""), out int data);
            return data;
        }
        public static bool Check(Mat src)
        {
            tpl = Cv2.ImRead(Application.StartupPath+"/tpl.bmp", ImreadModes.Grayscale);
            if (tpl.Empty())
            {
                MessageBox.Show("无法找到模板,请检查模板");
                return false;
            }
            if (src.Empty())
            {
                Console.WriteLine("读取图片失败");
                return false ;
            }
            if(src.Channels() != 1)
            {
                Cv2.CvtColor(src, src, ColorConversionCodes.BGR2GRAY);
            }


            // iniFileUtil.ReadString("user", "account", "");
            // 定义四个点
            Point2f[] points = GetPoints();

            if (points == null)
            {
                MessageBox.Show("请设置ROI");
                return false;
            }

            // 计算最小外接矩形
            Rect rect = Cv2.BoundingRect(points);

            // 提取ROI
            Mat srcRoi = new Mat(src, rect);
            Mat tplRoi = new Mat(tpl, rect);

            // 二值化
            Mat binaryImage = new Mat();
            double thresholdValue = 128; // 阈值，可以根据需要调整
            double maxValue = 255;       // 二值化后的最大值
            Cv2.Threshold(tplRoi, binaryImage, thresholdValue, maxValue, ThresholdTypes.BinaryInv);

            // 腐蚀操作
            Mat binaryImage1 = new Mat();
            Cv2.Erode(binaryImage, binaryImage1, null, new Point(-1, -1), 7); // 使用null作为核

            // 查找轮廓
            Point[][] contours;
            HierarchyIndex[] hierarchy;
            Cv2.FindContours(binaryImage1, out contours,out hierarchy, RetrievalModes.External, ContourApproximationModes.ApproxSimple);

            // 找到最大轮廓
            double maxArea = 0;
            int maxContourIndex = -1;
            for (int i = 0; i < contours.Length; i++)
            {
                double area = Cv2.ContourArea(contours[i]);
                if (area > maxArea)
                {
                    maxArea = area;
                    maxContourIndex = i;
                }
            }

            if (maxContourIndex == -1)
            {
                Console.WriteLine("Error: No contours found!");
                MessageBox.Show("无法找到轮廓");
                return false ;
            }

            // 计算最大轮廓的外接矩形
            Rect boundingRect = Cv2.BoundingRect(contours[maxContourIndex]);

            // 在原图上绘制外接矩形并填充为0
            Cv2.Rectangle(tplRoi, boundingRect.TopLeft, boundingRect.BottomRight, Scalar.Black, -1);

            // 调用CalcPix函数
            bool res = CalcProcess(srcRoi, boundingRect);

            // 在图像上显示结果
            //if (res)
            //{
            //    Cv2.PutText(srcRoi, "OK", new Point(10, 10), HersheyFonts.HersheyPlain, 1.0, Scalar.Green, 2);
            //}
            //else
            //{
            //    Cv2.PutText(srcRoi, "NG", new Point(10, 10), HersheyFonts.HersheyPlain, 1.0, Scalar.Red, 2);
            //}

            return res;
        }

    }
}

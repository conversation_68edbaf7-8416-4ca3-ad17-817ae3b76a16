﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="splitContainer2.Locked" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>304, 10</value>
  </metadata>
  <metadata name="statusStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>16, 10</value>
  </metadata>
  <metadata name="contextMenuStrip2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>502, 10</value>
  </metadata>
  <metadata name="statusStrip2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>160, 10</value>
  </metadata>
  <metadata name="contextMenuStrip4.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>756, 14</value>
  </metadata>
  <metadata name="Column1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column2.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column3.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column4.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column5.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column6.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column7.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column8.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1133, 11</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>47</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAAAAAAAEAIAASGgAAFgAAAIlQTkcNChoKAAAADUlIRFIAAAEAAAABAAgGAAAAXHKoZgAAAAlw
        SFlzAAAOxAAADsQBlSsOGwAAGcRJREFUeJzt3U122tjWBuB3K8Su3j32BAqvVaRb1AiKGkG4Iwhp2m7E
        GUE5I8C11gc0Q0ZQzgguHkE5Xcha4Ak4cq8cK9pfQxI2BhsB0pGE3qdxb/wnRNna2mefrXMEZJ3pjn5/
        +qtSdUSry46hikZiJ7QGUZ3Akcmy7/N/4BIA4MCd+6LqjXv86jLxk6PYJOsTKJqHF68jsxehKuoAzMPP
        CbK9UItGFS4Ej4OCKw8+5/vqQmTme9yj2oWVE9wylaxPgIiyU+oMwLTHBrver/CDu7bzAnXg8Z1cqwKp
        ZnOGtCrVcKgRZgwiGERf81UmgE6gegMAHH5scQAwvXEV6v0M1ToAOI4YVTSgakSknvX5UT48HHJEdY1p
        oACA28oX9/3BfP1iSxQ2AEzv3gjH4r5WVaTKMTclbSZIAJcQuL5iAKlcAYB7eDDJ9AQ3kPsAYNpjg5fe
        7w/S8wYUdZHZYhtRVqIAIYJBkD0AkBcXRQgMLAISlViuMoAgrf/x2lG/oeEUGwtwVFRhZjAAABFc+r5+
        zlvhMdMAYHrjKvy71wK0AIDFOdp2D4OCQs5x++JzlkVGqwHAtMcGO3dveMET3VPgPPh/OXePfvlk87VZ
        AyAqsdQzANMbVwHA8e/+VEiT1Xuip6nCFQdn/r+VvwAg7eFBagHAtMfG+cl7pz5OAIAXPlF8UUdjFAzS
        CgSJBwDTGzUAQFQ/soJPtDlVuOrgvwDgHtYGSR470QBgOsN3jshZksckohln10e190kdjEVAohJLLAPY
        7ww/QqSV1PGIaDEFzvW28jaJukAiGQAvfiJ7BGhi564K4LcEjrU+0x2dAIADtDc9ESJakWofAK6PX71d
        9xBrBwDTGzUcxf/W/XkiSoYPvHePamsV31kEJCqxtTIA0x4b2b37h/P8RPngq/62zpOGa2UAzo53Akh1
        nZ8louSJSBvAHyv/3Ko/YNpjIzvemK29RPniC/5YtVOQNQCiEls5ADg73gl49yfKHVH9COBgpZ+J+42m
        PTYAwPSfKL98CeoAcYcC8TOAnbsmAIgIL36inHJUW+E/B3G+P3YGsNcZ/gNwGS+iIvBvK3txnhVgEZCo
        xGIFANMbV0U93vmJiiIYsveXfVu8DEC95oanQ0QWCSRWAIhVA9jrjgYC/L7pSRGRPXHqAKwBEJXY0gBg
        2mMj8Hj3Jyqa3R8NhJuOPGV5BhAchIgKxoHfxKYBwFE08rWFKBHFoTHqdksDgML/XRgBiApHINVoZy73
        8GCy6HtYBCQqsaUBgK2/RAWmP6Lrd7Loy08GgGiLL2jSZ0REtjiKRvjPhcXAJwOA44c/yOE/UWGp6LMZ
        PGsARCX2ZABQoA4wASAqsmUt/M9kAPrz9BBEVGimM6wvWjb8yQDA6j/RFhGnCiBeADC9UYPVf6Lt4ajW
        sWAmgEVAohJbHAAUTP+JtojKtB9gxsIA4CgMa39E2yQq6s9aGABU0OD1T7Q9ntrI94kagP7M6T+i7bJo
        KpBFQKISWxgAnkoXiKjAFvQCzAUA9gAQbadFvQDzGYCvVQjH/0TbJnq+5yHWAIhKbC4AOJCq9bMgovQJ
        5nb2ns8ARKucAiTaQopfH39qLgAopMrLn2j7SKwMgE1ARFvrcTMQi4BEJTYXANgERLTFHJkZBswEANMZ
        8jFgom0WPOo/iD6czQAcMewCJNpejuLpDICIymU2ALANmGirPW4HngkA7AIk2nLy7BBADDcDJNpiqv95
        +OFMAFDROgcARNvr8X4fLAISlRgDAFGJzWT8e53Rt0UPDBDR9vBVfwMA9/jV5UwGwIufqAQetANzCEBU
        YhUAMO1xGBG8LM+FiCwLMoCfvGBqIIEWAFV8BgAV6UNeXLqHB5Poa6Y3rkK9pihOAEAEC7crKhOFfpFw
        qWYfcgkJl23+t3IJAO77AxcIV2sOOT4aCtQR7vcmwH+QMQVuoBhM34uDAVCZPPz9A+HNJvp7AwBfq1ED
        Wrgj1e+2zjkOVVyJ6AAqEyB6XwD+rVxGv5uI6QzrUXqdx99RxPGn+wQOBLj/43IU/1v3oAr9oorW451H
        FokyDmfHO4Hgz3Vfs4hUcSWQcwDwnRdnjy+QdZju16YDvwnIm41PcCX6CQB8OOfu0S9zW0+vy/RGDceX
        JgCoaMvmxaPAhQLnkMo5ACTx+wGC4OAIThTSzDwYKD4AwPVx7TQIAN2vTQBwoH+vecRP/u3Lk8cRMQ7T
        GdZF0AcAgcytWbYtFLhR1RP3+FU/rdcwvXHV8X+cAOldOArciOLMdyr9pC6O55j22GDnrimQUyD5rDF4
        P9IHkgvIzzHtsXF2vJPwtVuZZMEPAgCLgEQlJgCw3xmdhh+tlI77wHsAcI9qZ5uchOmNqwDgqDfe5Di5
        FEZb/3vlbJ0MaV2mN66Kev3ExtQqfwGA//3Fqc338dh+Z3SqgpNEshvFB9u/l4em2YDlYXBUp/t2XGuu
        HQBUcfXtuFZN8sT2O6PTbakJKHCjgqZ7WBtkeR6mM2wBgIicrXPRKHARDl2W1nZsiYaN6wwZFbhQqbSA
        5Mb3m4pW4hLIuY0hgQIXAPDtqNYIA8DX4A4u+i7uQXzIf5Ms/ET2usPLItcCFPoFAOIWRG0xnWF9lT+w
        pLK7tJj22Di7d+G5LS9+2qjBbMq0x0Z2vL4IXqf5OnMBYK87GgBA3HRRgYtvR7VGGicXVLTXLUZmS4Eb
        va1Ugfvpuzwx7bGR3bvBcwFWFVcKbeYpeC2z3x32nwoCeQ3Iz3nu/SRBoRMA+Hb06oBFQKISWysD8FXf
        pplK7XVGk6I1CYUpZiPvd5koCwDmp10VuNDbSjOP2csyi+6aqvis38PxfsHeU/B+gDQzgeujmqybAaT6
        Ry5AHyhWMVAhLfe4luuLHwguBNMbNwEA6l0GhcGgoefb0atWlue2ieujV6297rB+H9T007fjYr8fANjr
        jEyaNYEwAGh4t423HlDadznfqfQd9YoTAFT+co+TL4imJap+m86wIYKT6wJf+A+pvGxK2Ah1fVw7yfp8
        kqDfKy3seJdpZcSsARCVWDAN2B2t9BjQ9VEt9aUD9zqjcwBIe0pkE6q4AgD9XqkXbYxJxRE8RyD/JH1c
        XyrrzQKY3riadhOFStCfLdD8BgCRE6B4BSYqFvf41eV+5+tfAFbq1VnOq645BPCqACbJnci8qMlorzO6
        yuOMgAIXaTRCES3if39xCgCy6yX6kFcleDR3tYVAwkc1B0mdxHOCR2eTjHrJUMFp1udA5RFlmfud0VmS
        7fIsAhKVWAU/efVVVwJS8V8DsDLN4jsvzhz1cpUBKHCR9UM+VE7+98qZ7HoJPQ0pZq0MQCBV0xs1bFwE
        7uHBZK87usjTUlEKyeUDMrT93PcH7n53eJ5Eh6CjWl97CCA+TmCpDqCqfRHJRQBQxVWRmn5o+/hwzh1o
        Ii3CrAEQlZiY3qix7mKgvlQOADsLK+x1Ry6Qg9VVFR+uj2unmZ4Dld5ed+RufC0oPlSg628JHi1ACQsF
        wWjhxmQbIVbnO5V+lq+fV+GS79N+DfeodpHl+WxqulfGrhc8XHRb+QLkqOlLMUACXbIVR7Ue8xmgBecQ
        jENMe5z6OnG+8+IMALKaEYjWUXOPLKyE2xnWBfgIBNs5q8JVkbfB62dffwhX6n0jQAsIt5zW2V6S/e4I
        Cp1Ml0C/rXzIzcXzyPRi37l7IyJNARpzvTG7wcd7nZEr0HPfefkhyyXFVDAQJBAANvnhaC9B3blrIniE
        NzXRf+y97vBLFkuGRa3JNgjw8eE+7iIwEq6SZDrD37Jcc2C/N/pTfe9E5H5/uacIpIowO5Qdr2U6w/d5
        W5LLdL82Rb0o2C5/TwIDSMtRr7XfC9bSvD6sfUj5NBecCC4338hnzWlAItoOiQQACVoT+0kcaxlVnIkE
        6bEtNqf+TGfYenj3f8yBNJHygiyPBasIBXdJKJqyxpAxyGLk43539Ov1Ue19wqe4lv3O8COgrXWHwNCg
        HXy/M6xeH796m9yZxVGZbLqXp8oGfQAP2WwMwveX57rrrbXE9brEUnADAJHnGzxUpvu6WSO73kcBmgkd
        7sR0R1dAdisOP1gGv5XIAUVa+52vN9fHv1hbhMQ9PJjsd0cbHyexIYCj2oKFxqAkO6HislX5D4t/DRuv
        FZfpjk4SvPgBAA7QBgDTG5/bLqSZ3qgBTWG5OdF3pvt1kIci7SpYAyAqsQQDgLwxvfGpjYjuK84cgZUM
        QBWfbUz9AYAjdh6wiiOaGhP1/lx7jLyEo3enQEJpeEySxt0/Ojb8NoCiZQDrNwI95vheC0j/OXn3+NWl
        relAhab+C53OQ8OzvL33M3a9FgAIsHRqbH32bhpA+kMsgVSjnbZtDAWiHX42eVCuoqL1pAK8Au9gIQAA
        6c8GKHADADbmraPtovNENLxQ0l790b9rwFKR1YHTSupm9+Rr6DTAFCITSLQGIAITbUaZ+oUTzgYA6Twf
        MG09tkAl6KiU1K+2+GwtxirBtGbfxmslebN77jVSfolEsQhIVGKJBwAJhgFA2q3B0+lAII0pwejZg7SZ
        zrAVtsyWk6RZY3hE8WvqSZaiUDtbJx8Awi42G41Bftif72iyMwLBkl92ClPLGn+yEM6Vbx2xEGxsvEZy
        9OfUhgA2GoOiAJP00uGq2k/qWM/JY+MPlYdA1t0XINbhrU3xqOBMwu6yjY8F3Nh6Yi1P8/5UTiwCEpVY
        qgHAVmMQbit97HqJZABS1sYfKqVUA4CtxqBwRuBTErMBviL96n/YZUeUtVQDgM3GIF+kv+lsgEK/2Fht
        R6ZbneWn8YfKiTUAohJLPQBYaww6rA02nQ5UC+m/6X5tCrSa9usQxZF+ALDYGBSu3LPy457Rgz/4/jL1
        AqDkcKdjKi9rQwAbjUG+U+k76q0cAKLK/7eUl602vXFV1Guk+RpEq7BYA0i/Mcg9PJjsdUafgdWeZrNS
        +Ue0AAYLf5QfLAISlZjVAGCjMSjawEOgsTKAYMnv9Kf+THtsVL3X6yypTZQWqwHARmNQtBRT3BkBFTvp
        P3a9VrrLaxGtzmoAiBqDbDxsE3tG4NbOkt9B9Z+3f8oX1gCISsx6AAgbg/ppv068KUH9lPaOtdEqsWz+
        oTyyHwBE6qY3agD3C3qkIZoSfG460Lew4y8bfyjPMhkChE1BQMqNQQo9F8hcAFBFtDddqq/Pxh/Ku4xq
        AMFju6Y9PkkzBXePX/X3uqO5pcNtVf7Z+EN5xyIgUYllGgDCHXFO03yN6QYf8mAsLhUrq/6w8YfyLtMA
        EO6Ic5rma0Tr+zvqvQMsbva5c9cUETb+UK5lGgAEUk27MSh6+GivO7oQ4He1UPkHAJH0dqElSgprAEQl
        lnkACHfG6af9OsFmH1J1j9PdtjnqcRBFNc3XIUpC9gEAaJjOsA4AaT6V5x6/6pvuKP2tofxwsw8W/yjn
        FLjIPAAAMzvktNJ8Hfeolur8f9j4Y2VbbaIk5CIA2GoMSpvj/zjhnZ+KJCcBgIiykKsAYKMxKC2mPTYK
        7w0TACqSXAUAG41BqWHjDxVQRRQDCH7P+kQAO41BaWHjDxVRrjIAwF5fQJJMb9TgvD8VUe4CABHZk7sA
        EDUG2ViqOynig9N/VEi5CwDAtDGolfV5LGN64yoAsPmHiiqXAQCQN6Y9PgGAPDcGhRudsO2XCkkUg5wG
        ACKyIbcBIGwKAnLcFxDudMQEgAqr4gtcJ+uzWCBsCgJyGgBMZ9gS4VZfVGwVCC6hWZ/GPIFUgeBCy2Nj
        ULjBCVGh5XYIEMljY1DY+FPP+jyINpX7AEBE6cl9AMhjY1CwsxFLf1R8uQ8AQL4ag0xvXIV6b5Z/J1Ha
        9Ofg/9e7GflOYfoA5I3pjU+B+2W+s8JVf5IW/RGny7THBvBsvJQ1UaF8EwUJAESUhop7WBvsd0dZn8dS
        07bbjPoCgjsIUIpVf1QMLM0NJ3EXi+Unr25zutu0xybPbeyRwmQAUdedaY/PsvgPO+1MzLr5R/Fr2i/h
        qNZtDnOih6pSHd5ZDGoAgoADDNI6fLSU/qYKEwCirjvZ+XEKTJcRt8L0xlX1vVy0/YrAmN64mubFooKG
        1fepP6I/5klaL+EoGjZ/eY6PBlIMABBpbHyMfyuXhQkAU6LvTG98ZrMY6OjdKfK03p96TQCp7HFg2mMj
        8KwuEefAb4b/TG3XJhX/tViMAAp9jRSHq5JAQHPfH7jFCwBElJgKEGwRBACCfCwOuoyo9xHAHzZey/RG
        DShyNe8vqm+QUgaA3Wmx1RpVeQ2kUzjLaq9GEamnNVRLcgeqQmYAAjT2O6PT6+PaaVqvEVX9Re8+Zj/y
        nxX8cY0a7mFtkORxg/T/7p3t93tf30l+XwjR7FZrdvTuFCk0sCXZiyIAsNcdDcIPCpEBRHzVt0Cw8WfS
        x97rDP8Bgost6WMnQaETvX35G5Dcqkn7ndEpMlzeXBWuOpXfkrprms6w5Yh8TOJY6/IlyFSTCtamN2o4
        iv9tcgxVXAHAt+NatZAZABElIwgAiuAOkq9Md6koupvuV9c9+iWRCrJpj43seh8F+X7cVyBV2blrhx++
        3eRYpjNshQfNdHMTERj4d3+b9viPTbMa0xnWBdJe/p3pEh9/h+fzxyYPtEXz/uLj742vU7mfbhUgTP2C
        j4q8u83Z9VHt/SYHML1xVfy7v/Oa9j9FgXO9rbxd56IxneE7RyTVbdNXpaqX6rz8L7B6c5Dpfm0CgKh+
        zNOKTcHwBv9dZyhgeqNGFEiSeE9R0f/bUa2xTQEgGBfDOQUA3L74HOeCMJ1h3YlW9xFppXl+aVKFqyIn
        uH3xGXi+LmD+b/QaAMTBiQANS6e4Eg2zUnFw5qPyaVkgMJ1hXUT+FKD53PdlTrXvA38BwHMZQXTHd4B3
        yf9d6icAuD561RIAMN3RSfhimadMSVLVS4hMAEAEwX9shdEovVfU83SXSJICA1GdwAne//R9F/Q9q2rw
        +xOZSLCM3f3vEVq19kxBgsK/T1fkvmNQFQ2omlSzUMUHALg+rp2yCEhUYkEAuL87bpUwikZ3+2lqKHP/
        2D4CNCAy8zuV6f8Uz4M7Yj36Xd6/lWK+qel70vthWPA7svd+mAEQlYwvmNaHggDgazgNWMxISkQriDJ+
        hAEgqkYWYWEQIkoOhwBEJcYAQFQ6lcn0Xw8/rdAvAkl9ySkiys7DpqpHGYDkfhFDIkoOhwBEJTYTAAQ6
        AaRQawIQUXzRg0CR2QxAZVLQpioiWgOHAEQlEmT592YCgO9g4GS4hhoRpUzDp0NDzACISowBgKhEHj4I
        BDwKAEXZKJSI1vTgQSCAGQBRuURP/oYYAIhKbC4AKHBRtA1CiCiexwuRzmcACpfNQETbR4Gbx5+bCwAC
        XAJIZONBIsqVuWXI5wKAL3AdOydDRDYp5p72ZRGQqMTmA0Cw6QIRbRmJMwTAv5VL7HpWToiI7PFFlgcA
        9/2Bu9cd3QjwHzunRURWiLIGQET3ngoAl2AzENFWWbQ1+cIAICqXEGUAINoSqrha9PmFAcAXnbAXgGiL
        CCaLPr14CMCpQKKtIjo/AwCwCEhUaosDAHsBiLaKDz9+BuC+P3D3OqMrABDBz2meGBFZ4MwuBhp5eghw
        XzRgACAquEVTgMAzAUAUwQ8I+wGIikqhX577OouARCX2ZADwnSAD4EYhRMW16AnAh57OAP6tBD/I2QCi
        wvKxeP4/8mQAcN8fuACw1xldcSaAqKBUB899mTUAohJbGgBEdADIGwvnQkQJUuDm8TLgjy0NAL5i4AgY
        AIiKJprKf8byIYDzcgBlIZCoaFQSCADu4cFkrzv8IpBfEzkrIrJjSQEQYBGQqNRiBQBRZwBRZgBEBaGK
        q2UFQCBmAPDh9x3Iu81Pi4hsEMh5vO+Laa8zmgB8PJioCHzV3xLLAIhoO8UOAAL0w3/y4SCinIpW/41z
        9wdWCAC+U+kDgKMeAwBRTqngbJXvj10DiOx3h322BhPljwI3elupAvcP8y2zcg3Al5enjnoMAEQ5I4qz
        bzEv/AiLgEQltvIQAOAwgChvovQ/buofWSsD8OXlqajX5BbiRPmgwOmqFz+wZgYAAKY7OnGA9ro/T0TJ
        UODi21Gtsc7PsgZAVGJrZwBAVAsAWA8gsk+BGwBQqdTdw4PJOsfYKAPwb1+eAIDs3tW5XgCRXaraAAD3
        aL2LH9gwA4iY9tjI7t2AQYDIDl/1rXv8qr/pcRIJAEAYBHa8vgheJ3VMIpqlwI2qNuL2+i/DIiBRiSWW
        AUT2O6PT8Mh8aIgoIdEmnyovm+sW/BZJPABETGdYF5EzAXcXJlqXAjeiOLs+rp2mcfzUAkDE9EYNABDF
        KYMBUVz6CQhm2tbp8Isr9QDwkOmNGo5qi30DRPNUcSVA33cq/STT/OewCEhUYlYzgIhpjw0AYOeuKZAm
        pw6pjBS4Eei5D+ccANyjX2Kt5JukTALAU0xv1HB8aar4DTYV0bZR6BdRZ+CHW3ZlccE/lqsAsEgQFNBQ
        oB5+qs6lySnvFLgQxcAXCRp2bl8M0izmrYs1AKISy30GsIhpjw1+8uqOjwYAQLSqkCqnGcmW8Em8SwAQ
        xcCHTgBcJtWia0shA8BzpgXGn7w6FHVHpQoAKloH1LC2QMuo4gqCCQAIdAKVyTSVF3Xdw9ogw9NL1NYF
        gDhmg4QYRzWsL4gJAgWAsObAZc+2w8xFrUERDgB8kUuIhmPzysTW/HtelDIArCoackw/4WvVgVTvv2Mm
        cASf4XAkUVEvPCBu+AlXwhQ84jv3FzZ8dYuWjmeBRUCiEmMGYFH0XMSMuWzisfns4jlpZR7RnnNRGh3r
        XB6k2ovMpt/3tmmMnXf/D7l2eUmwUFjmAAAAAElFTkSuQmCC
</value>
  </data>
</root>
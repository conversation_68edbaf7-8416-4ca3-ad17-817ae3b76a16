﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HalconDotNet;
using System.Windows.Forms;
using System.Drawing;
namespace 磨边机视觉定位系统
{
   public class Process
    {
        public string FilePath;
        public string FileRecordPath;
        public bool PowerState;
        public int CamRunState = 0;//3运行状态，1标定状态,2模板状态,0无状态
        public  HObject ChoseZoon;

        public string ProductCode;
        public bool IsChoseProduct;

        public int CalNumLeft;
        public int CalNumRight;
        public string IPAdress;
        
        public bool SuccToCnctPlc;

        public HoleParamModel Holeparammodel;
        public ErModelParam m_ErModelParam;
        
        public bool BnThread;

        public string VerionWord;
        public int Level;//0-操作员，1-工程师，2-高级用户

        public double JugleAngle;
        public string CamNm1;
        public string CamNm2;

        public bool IsStartSoft;

       public bool IsLoadCamProcess;
    }
    public struct HoleParamModel
    {
        public double score;
        public double numlevel;
        public double minangle;
        public double maxangle;
        public double angelstep;
        public double mincontrast;
        public double highcontrast;
        public double lowcontrast;
        public double minsize;
        public AlModelParam ampm;
        public 定位模板 Modelparam;
    }
    public struct ErModelParam
    {
        public double length1;
        public double length2;
        public HObject hmregion;
    }
    public struct AlModelParam
    {
        public double r2_1x;
        public double r2_1y;
        public double r2_1a;
        public double r2_1l1;
        public double r2_1l2;

        public double r2_2x;
        public double r2_2y;
        public double r2_2a;
        public double r2_2l1;
        public double r2_2l2;

        public double x1;
        public double y1;

        public double x2;
        public double y2;
    }

    public struct 定位模板
    {
        public HTuple ModelId;
        public HObject Cregion;
        public double Cmx;
        public double Cmy;
        public double Cma;
    }

    public class CalResulet
    {
        struct Result
        {
            public double[,] fs;
            public double[,] rms;
        }
        public double[] CamxLeft = new double[4];
        public double[] CamyLeft = new double[4];
        public double[] MacxLeft = new double[4];
        public double[] MacyLeft = new double[4];

        public double[] CamxRight = new double[4];
        public double[] CamyRight = new double[4];
        public double[] MacxRight = new double[4];
        public double[] MacyRight = new double[4];

        public HTuple Hom2DLeft = new HTuple();
        public HTuple Hom2DRight = new HTuple();
        public bool PixelToPoint(PointF pixel,HTuple Hom2d, out float x, out float y)
        {
            bool ret = false;
            //point = new PointF();
            x = 0;
            y = 0;
            try
            {
                HTuple px, py, wx, wy;
                px = pixel.X;
                py = pixel.Y;
                HOperatorSet.ProjectiveTransPixel(Hom2d, px, py, out wx, out wy);
                x = wx[0].F;
                y = wy[0].F;
                ret = true;
            }
            catch
            {
                ret = false;
            }
            return ret;
        }
        //矩阵
        private double[,] Ni(double[,] a, int n)//逆矩阵
        {
            double[,] b = new double[10, 10];
            int i, j, k; double t;
            for (i = 0; i < n; i++)
            {
                for (j = 0; j < (2 * n); j++)
                {
                    if (j < n) b[i, j] = a[i, j];
                    else if (j == n + i) b[i, j] = 1;
                    else b[i, j] = 0;
                }
            }
            for (i = 0; i < n; i++)
            {
                for (k = 0; k < n; k++)
                {
                    if (k != i)
                    {
                        t = b[k, i] / b[i, i];
                        for (j = 0; j < (2 * n); j++) b[k, j] = b[k, j] - b[i, j] * t;
                    }
                }
            }
            for (i = 0; i < n; i++)
            {
                t = b[i, i];
                for (j = 0; j < (2 * n); j++)
                    b[i, j] = b[i, j] / t;
            }
            double[,] c = new double[n, n];
            for (i = 0; i < n; i++)
            {
                for (j = 0; j < n; j++)
                    c[i, j] = b[i, j + n];
            }
            return c;
        }

        public bool CamCalXY(double[] xx, double[] yy, double[] rr, double[] cc, out HTuple Hom2D)
        {
            Hom2D = new HTuple();
            double[,] A = new double[3, 3];
            double[,] NA = new double[3, 3];
            double[,] B = new double[3, 2];
            int n = xx.Length;
            int i, j, k;
            double x, y, X, Y;
            A[0, 0] = n;
            for (i = 0; i < n; i++)
            {
                x = xx[i];
                y = yy[i];
                X = rr[i];
                Y = cc[i];
                A[0, 1] += x; A[0, 2] += y;
                A[1, 0] += x; A[1, 1] += x * x; A[1, 2] += x * y;
                A[2, 0] += y; A[2, 1] += x * y; A[2, 2] += y * y;
                B[0, 0] += X; B[0, 1] += Y;
                B[1, 0] += x * X; B[1, 1] += x * Y;
                B[2, 0] += y * X; B[2, 1] += y * Y;
            }
            NA = Ni(A, 3);
            //矩阵相乘
            Result result1 = new Result();
            result1.fs = new double[3, 2];
            for (i = 0; i < 3; i++)
            {
                for (j = 0; j < 2; j++)
                    result1.fs[i, j] = 0;
            }
            for (i = 0; i < 3; i++) //c=a*b
            {
                for (j = 0; j < 2; j++)
                {
                    for (k = 0; k < 3; k++)
                    {
                        result1.fs[i, j] += NA[i, k] * B[k, j];
                    }
                }
            }
            Hom2D[0] = result1.fs[1, 0];
            Hom2D[1] = result1.fs[2, 0];
            Hom2D[2] = result1.fs[0, 0];
            Hom2D[3] = result1.fs[1, 1];
            Hom2D[4] = result1.fs[2, 1];
            Hom2D[5] = result1.fs[0, 1];
            return true;
        }

        public bool CamCalxyH(HTuple Qx,HTuple Qy,HTuple Px,HTuple Py,out HTuple Hom2D)
        {
            Hom2D = new HTuple();
            HTuple Qc =new HTuple(1, 1, 1, 1);
            HTuple Pc = new HTuple(1, 1, 1, 1);
            HOperatorSet.HomVectorToProjHomMat2d(Qx, Qy,Qc, Px, Py,Pc, "normalized_dlt", out Hom2D);

            return true;
        }
        
    }
   
    public  class Image_Zoom
     {
        private int current_beginRow, current_beginCol, current_endRow, current_endCol;
        private int zoom_beginRow, zoom_beginCol, zoom_endRow, zoom_endCol;
        public HTuple X = new HTuple();
        public HTuple Y = new HTuple();
        public void DispImageFit(HObject t_image, HWindowControl hw_ctrl)
        {
            if (t_image != null)
            {
                hw_ctrl.HalconWindow.ClearWindow();
                HTuple hWindowHandle = hw_ctrl.HalconWindow;
                int hw_width = hw_ctrl.Size.Width;
                int hw_height = hw_ctrl.Size.Height;

                HTuple width, height;
                HOperatorSet.GetImageSize(t_image, out width, out height);

                double realw = 1.0 * width[0].I / hw_width;
                double realh = 1.0 * height[0].I / hw_height;
                HOperatorSet.SetPart(hWindowHandle, 0, 0, realh * hw_height, realw * hw_width);
                X = realw * hw_width;
                Y = realh * hw_height;
                HOperatorSet.DispObj(t_image, hWindowHandle);

            }
        }
        public void DispImageZoom(HObject t_image, HWindowControl hw_ctrl, HTuple mode, double Mouse_row, double Mouse_col)
        {
            if (t_image != null)
            {
                HTuple width, height;
                HOperatorSet.GetImageSize(t_image, out width, out height);
                int hv_imageWidth, hv_imageHeight;
                hv_imageWidth = width;
                hv_imageHeight = height;
                try
                {
                    hw_ctrl.HalconWindow.GetPart(out current_beginRow, out current_beginCol, out current_endRow, out current_endCol);
                }
                catch (Exception e)
                {
                  //  MessageBox.Show(e.ToString());
                    return;
                }


                if (mode > 0)//图像放大
                {
                    zoom_beginRow = (int)(current_beginRow + (Mouse_row - current_beginRow) * 0.300d);
                    zoom_beginCol = (int)(current_beginCol + (Mouse_col - current_beginCol) * 0.300d);
                    zoom_endRow = (int)(current_endRow - (current_endRow - Mouse_row) * 0.300d);
                    zoom_endCol = (int)(current_endCol - (current_endCol - Mouse_col) * 0.300d);
                }
                else//图像缩小
                {
                    zoom_beginRow = (int)(Mouse_row - (Mouse_row - current_beginRow) / 0.700d);
                    zoom_beginCol = (int)(Mouse_col - (Mouse_col - current_beginCol) / 0.700d);
                    zoom_endRow = (int)(Mouse_row + (current_endRow - Mouse_row) / 0.700d);
                    zoom_endCol = (int)(Mouse_col + (current_endCol - Mouse_col) / 0.700d);
                }

                try
                {
                    int hw_width, hw_height;
                    hw_width = hw_ctrl.WindowSize.Width;
                    hw_height = hw_ctrl.WindowSize.Height;

                    bool _isOutOfArea = true;
                    bool _isOutOfSize = true;
                    bool _isOutOfPixel = true;//避免像素过大

                    _isOutOfArea = zoom_beginRow >= hv_imageHeight || zoom_beginRow >= hv_imageWidth || zoom_endRow <= 0 || zoom_endCol <= 0;
                    _isOutOfSize = (zoom_endRow - zoom_beginRow) > hv_imageHeight * 20 || (zoom_endCol - zoom_beginCol) > hv_imageWidth * 20;
                    _isOutOfPixel = hw_height / (zoom_endRow - zoom_beginRow) > 500 || hw_width / (zoom_endCol - zoom_beginCol) > 500;

                    if (_isOutOfArea || _isOutOfSize)
                    {
                        DispImageFit(t_image, hw_ctrl);
                    }
                    if (!_isOutOfPixel)
                    {
                        hw_ctrl.HalconWindow.ClearWindow();
                        hw_ctrl.HalconWindow.SetPaint(new HTuple("default"));
                        hw_ctrl.HalconWindow.SetPart(zoom_beginRow, zoom_beginCol, zoom_endRow, zoom_endCol);
                        hw_ctrl.HalconWindow.DispObj(t_image);
                    }
                }
                catch (Exception e)
                {
                    //MessageBox.Show(e.ToString());
                    // DispImageFit(t_image, hw_ctrl);
                    //throw;
                }

            }

        }
    }

    public class Function
    {
        public void set_display_font(HTuple hv_WindowHandle, HTuple hv_Size, HTuple hv_Font,
         HTuple hv_Bold, HTuple hv_Slant)
        {



            // Local iconic variables 

            // Local control variables 

            HTuple hv_OS = null, hv_BufferWindowHandle = new HTuple();
            HTuple hv_Ascent = new HTuple(), hv_Descent = new HTuple();
            HTuple hv_Width = new HTuple(), hv_Height = new HTuple();
            HTuple hv_Scale = new HTuple(), hv_Exception = new HTuple();
            HTuple hv_SubFamily = new HTuple(), hv_Fonts = new HTuple();
            HTuple hv_SystemFonts = new HTuple(), hv_Guess = new HTuple();
            HTuple hv_I = new HTuple(), hv_Index = new HTuple(), hv_AllowedFontSizes = new HTuple();
            HTuple hv_Distances = new HTuple(), hv_Indices = new HTuple();
            HTuple hv_FontSelRegexp = new HTuple(), hv_FontsCourier = new HTuple();
            HTuple hv_Bold_COPY_INP_TMP = hv_Bold.Clone();
            HTuple hv_Font_COPY_INP_TMP = hv_Font.Clone();
            HTuple hv_Size_COPY_INP_TMP = hv_Size.Clone();
            HTuple hv_Slant_COPY_INP_TMP = hv_Slant.Clone();

            // Initialize local and output iconic variables 
            //This procedure sets the text font of the current window with
            //the specified attributes.
            //It is assumed that following fonts are installed on the system:
            //Windows: Courier New, Arial Times New Roman
            //Mac OS X: CourierNewPS, Arial, TimesNewRomanPS
            //Linux: courier, helvetica, times
            //Because fonts are displayed smaller on Linux than on Windows,
            //a scaling factor of 1.25 is used the get comparable results.
            //For Linux, only a limited number of font sizes is supported,
            //to get comparable results, it is recommended to use one of the
            //following sizes: 9, 11, 14, 16, 20, 27
            //(which will be mapped internally on Linux systems to 11, 14, 17, 20, 25, 34)
            //
            //Input parameters:
            //WindowHandle: The graphics window for which the font will be set
            //Size: The font size. If Size=-1, the default of 16 is used.
            //Bold: If set to 'true', a bold font is used
            //Slant: If set to 'true', a slanted font is used
            //
            HOperatorSet.GetSystem("operating_system", out hv_OS);
            // dev_get_preferences(...); only in hdevelop
            // dev_set_preferences(...); only in hdevelop
            if ((int)((new HTuple(hv_Size_COPY_INP_TMP.TupleEqual(new HTuple()))).TupleOr(
                new HTuple(hv_Size_COPY_INP_TMP.TupleEqual(-1)))) != 0)
            {
                hv_Size_COPY_INP_TMP = 16;
            }
            if ((int)(new HTuple(((hv_OS.TupleSubstr(0, 2))).TupleEqual("Win"))) != 0)
            {
                //Set font on Windows systems
                try
                {
                    //Check, if font scaling is switched on
                    HOperatorSet.OpenWindow(0, 0, 256, 256, 0, "buffer", "", out hv_BufferWindowHandle);
                    HOperatorSet.SetFont(hv_BufferWindowHandle, "-Consolas-16-*-0-*-*-1-");
                    HOperatorSet.GetStringExtents(hv_BufferWindowHandle, "test_string", out hv_Ascent,
                        out hv_Descent, out hv_Width, out hv_Height);
                    //Expected width is 110
                    hv_Scale = 110.0 / hv_Width;
                    hv_Size_COPY_INP_TMP = ((hv_Size_COPY_INP_TMP * hv_Scale)).TupleInt();
                    HOperatorSet.CloseWindow(hv_BufferWindowHandle);
                }
                // catch (Exception) 
                catch (HalconException HDevExpDefaultException1)
                {
                    HDevExpDefaultException1.ToHTuple(out hv_Exception);
                    //throw (Exception)
                }
                if ((int)((new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("Courier"))).TupleOr(
                    new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("courier")))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "Courier New";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("mono"))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "Consolas";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("sans"))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "Arial";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("serif"))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "Times New Roman";
                }
                if ((int)(new HTuple(hv_Bold_COPY_INP_TMP.TupleEqual("true"))) != 0)
                {
                    hv_Bold_COPY_INP_TMP = 1;
                }
                else if ((int)(new HTuple(hv_Bold_COPY_INP_TMP.TupleEqual("false"))) != 0)
                {
                    hv_Bold_COPY_INP_TMP = 0;
                }
                else
                {
                    hv_Exception = "Wrong value of control parameter Bold";
                    throw new HalconException(hv_Exception);
                }
                if ((int)(new HTuple(hv_Slant_COPY_INP_TMP.TupleEqual("true"))) != 0)
                {
                    hv_Slant_COPY_INP_TMP = 1;
                }
                else if ((int)(new HTuple(hv_Slant_COPY_INP_TMP.TupleEqual("false"))) != 0)
                {
                    hv_Slant_COPY_INP_TMP = 0;
                }
                else
                {
                    hv_Exception = "Wrong value of control parameter Slant";
                    throw new HalconException(hv_Exception);
                }
                try
                {
                    HOperatorSet.SetFont(hv_WindowHandle, ((((((("-" + hv_Font_COPY_INP_TMP) + "-") + hv_Size_COPY_INP_TMP) + "-*-") + hv_Slant_COPY_INP_TMP) + "-*-*-") + hv_Bold_COPY_INP_TMP) + "-");
                }
                // catch (Exception) 
                catch (HalconException HDevExpDefaultException1)
                {
                    HDevExpDefaultException1.ToHTuple(out hv_Exception);
                    //throw (Exception)
                }
            }
            else if ((int)(new HTuple(((hv_OS.TupleSubstr(0, 2))).TupleEqual("Dar"))) != 0)
            {
                //Set font on Mac OS X systems. Since OS X does not have a strict naming
                //scheme for font attributes, we use tables to determine the correct font
                //name.
                hv_SubFamily = 0;
                if ((int)(new HTuple(hv_Slant_COPY_INP_TMP.TupleEqual("true"))) != 0)
                {
                    hv_SubFamily = hv_SubFamily.TupleBor(1);
                }
                else if ((int)(new HTuple(hv_Slant_COPY_INP_TMP.TupleNotEqual("false"))) != 0)
                {
                    hv_Exception = "Wrong value of control parameter Slant";
                    throw new HalconException(hv_Exception);
                }
                if ((int)(new HTuple(hv_Bold_COPY_INP_TMP.TupleEqual("true"))) != 0)
                {
                    hv_SubFamily = hv_SubFamily.TupleBor(2);
                }
                else if ((int)(new HTuple(hv_Bold_COPY_INP_TMP.TupleNotEqual("false"))) != 0)
                {
                    hv_Exception = "Wrong value of control parameter Bold";
                    throw new HalconException(hv_Exception);
                }
                if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("mono"))) != 0)
                {
                    hv_Fonts = new HTuple();
                    hv_Fonts[0] = "Menlo-Regular";
                    hv_Fonts[1] = "Menlo-Italic";
                    hv_Fonts[2] = "Menlo-Bold";
                    hv_Fonts[3] = "Menlo-BoldItalic";
                }
                else if ((int)((new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("Courier"))).TupleOr(
                    new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("courier")))) != 0)
                {
                    hv_Fonts = new HTuple();
                    hv_Fonts[0] = "CourierNewPSMT";
                    hv_Fonts[1] = "CourierNewPS-ItalicMT";
                    hv_Fonts[2] = "CourierNewPS-BoldMT";
                    hv_Fonts[3] = "CourierNewPS-BoldItalicMT";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("sans"))) != 0)
                {
                    hv_Fonts = new HTuple();
                    hv_Fonts[0] = "ArialMT";
                    hv_Fonts[1] = "Arial-ItalicMT";
                    hv_Fonts[2] = "Arial-BoldMT";
                    hv_Fonts[3] = "Arial-BoldItalicMT";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("serif"))) != 0)
                {
                    hv_Fonts = new HTuple();
                    hv_Fonts[0] = "TimesNewRomanPSMT";
                    hv_Fonts[1] = "TimesNewRomanPS-ItalicMT";
                    hv_Fonts[2] = "TimesNewRomanPS-BoldMT";
                    hv_Fonts[3] = "TimesNewRomanPS-BoldItalicMT";
                }
                else
                {
                    //Attempt to figure out which of the fonts installed on the system
                    //the user could have meant.
                    HOperatorSet.QueryFont(hv_WindowHandle, out hv_SystemFonts);
                    hv_Fonts = new HTuple();
                    hv_Fonts = hv_Fonts.TupleConcat(hv_Font_COPY_INP_TMP);
                    hv_Fonts = hv_Fonts.TupleConcat(hv_Font_COPY_INP_TMP);
                    hv_Fonts = hv_Fonts.TupleConcat(hv_Font_COPY_INP_TMP);
                    hv_Fonts = hv_Fonts.TupleConcat(hv_Font_COPY_INP_TMP);
                    hv_Guess = new HTuple();
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP);
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-Regular");
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "MT");
                    for (hv_I = 0; (int)hv_I <= (int)((new HTuple(hv_Guess.TupleLength())) - 1); hv_I = (int)hv_I + 1)
                    {
                        HOperatorSet.TupleFind(hv_SystemFonts, hv_Guess.TupleSelect(hv_I), out hv_Index);
                        if ((int)(new HTuple(hv_Index.TupleNotEqual(-1))) != 0)
                        {
                            if (hv_Fonts == null)
                                hv_Fonts = new HTuple();
                            hv_Fonts[0] = hv_Guess.TupleSelect(hv_I);
                            break;
                        }
                    }
                    //Guess name of slanted font
                    hv_Guess = new HTuple();
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-Italic");
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-ItalicMT");
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-Oblique");
                    for (hv_I = 0; (int)hv_I <= (int)((new HTuple(hv_Guess.TupleLength())) - 1); hv_I = (int)hv_I + 1)
                    {
                        HOperatorSet.TupleFind(hv_SystemFonts, hv_Guess.TupleSelect(hv_I), out hv_Index);
                        if ((int)(new HTuple(hv_Index.TupleNotEqual(-1))) != 0)
                        {
                            if (hv_Fonts == null)
                                hv_Fonts = new HTuple();
                            hv_Fonts[1] = hv_Guess.TupleSelect(hv_I);
                            break;
                        }
                    }
                    //Guess name of bold font
                    hv_Guess = new HTuple();
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-Bold");
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-BoldMT");
                    for (hv_I = 0; (int)hv_I <= (int)((new HTuple(hv_Guess.TupleLength())) - 1); hv_I = (int)hv_I + 1)
                    {
                        HOperatorSet.TupleFind(hv_SystemFonts, hv_Guess.TupleSelect(hv_I), out hv_Index);
                        if ((int)(new HTuple(hv_Index.TupleNotEqual(-1))) != 0)
                        {
                            if (hv_Fonts == null)
                                hv_Fonts = new HTuple();
                            hv_Fonts[2] = hv_Guess.TupleSelect(hv_I);
                            break;
                        }
                    }
                    //Guess name of bold slanted font
                    hv_Guess = new HTuple();
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-BoldItalic");
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-BoldItalicMT");
                    hv_Guess = hv_Guess.TupleConcat(hv_Font_COPY_INP_TMP + "-BoldOblique");
                    for (hv_I = 0; (int)hv_I <= (int)((new HTuple(hv_Guess.TupleLength())) - 1); hv_I = (int)hv_I + 1)
                    {
                        HOperatorSet.TupleFind(hv_SystemFonts, hv_Guess.TupleSelect(hv_I), out hv_Index);
                        if ((int)(new HTuple(hv_Index.TupleNotEqual(-1))) != 0)
                        {
                            if (hv_Fonts == null)
                                hv_Fonts = new HTuple();
                            hv_Fonts[3] = hv_Guess.TupleSelect(hv_I);
                            break;
                        }
                    }
                }
                hv_Font_COPY_INP_TMP = hv_Fonts.TupleSelect(hv_SubFamily);
                try
                {
                    HOperatorSet.SetFont(hv_WindowHandle, (hv_Font_COPY_INP_TMP + "-") + hv_Size_COPY_INP_TMP);
                }
                // catch (Exception) 
                catch (HalconException HDevExpDefaultException1)
                {
                    HDevExpDefaultException1.ToHTuple(out hv_Exception);
                    //throw (Exception)
                }
            }
            else
            {
                //Set font for UNIX systems
                hv_Size_COPY_INP_TMP = hv_Size_COPY_INP_TMP * 1.25;
                hv_AllowedFontSizes = new HTuple();
                hv_AllowedFontSizes[0] = 11;
                hv_AllowedFontSizes[1] = 14;
                hv_AllowedFontSizes[2] = 17;
                hv_AllowedFontSizes[3] = 20;
                hv_AllowedFontSizes[4] = 25;
                hv_AllowedFontSizes[5] = 34;
                if ((int)(new HTuple(((hv_AllowedFontSizes.TupleFind(hv_Size_COPY_INP_TMP))).TupleEqual(
                    -1))) != 0)
                {
                    hv_Distances = ((hv_AllowedFontSizes - hv_Size_COPY_INP_TMP)).TupleAbs();
                    HOperatorSet.TupleSortIndex(hv_Distances, out hv_Indices);
                    hv_Size_COPY_INP_TMP = hv_AllowedFontSizes.TupleSelect(hv_Indices.TupleSelect(
                        0));
                }
                if ((int)((new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("mono"))).TupleOr(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual(
                    "Courier")))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "courier";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("sans"))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "helvetica";
                }
                else if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("serif"))) != 0)
                {
                    hv_Font_COPY_INP_TMP = "times";
                }
                if ((int)(new HTuple(hv_Bold_COPY_INP_TMP.TupleEqual("true"))) != 0)
                {
                    hv_Bold_COPY_INP_TMP = "bold";
                }
                else if ((int)(new HTuple(hv_Bold_COPY_INP_TMP.TupleEqual("false"))) != 0)
                {
                    hv_Bold_COPY_INP_TMP = "medium";
                }
                else
                {
                    hv_Exception = "Wrong value of control parameter Bold";
                    throw new HalconException(hv_Exception);
                }
                if ((int)(new HTuple(hv_Slant_COPY_INP_TMP.TupleEqual("true"))) != 0)
                {
                    if ((int)(new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("times"))) != 0)
                    {
                        hv_Slant_COPY_INP_TMP = "i";
                    }
                    else
                    {
                        hv_Slant_COPY_INP_TMP = "o";
                    }
                }
                else if ((int)(new HTuple(hv_Slant_COPY_INP_TMP.TupleEqual("false"))) != 0)
                {
                    hv_Slant_COPY_INP_TMP = "r";
                }
                else
                {
                    hv_Exception = "Wrong value of control parameter Slant";
                    throw new HalconException(hv_Exception);
                }
                try
                {
                    HOperatorSet.SetFont(hv_WindowHandle, ((((((("-adobe-" + hv_Font_COPY_INP_TMP) + "-") + hv_Bold_COPY_INP_TMP) + "-") + hv_Slant_COPY_INP_TMP) + "-normal-*-") + hv_Size_COPY_INP_TMP) + "-*-*-*-*-*-*-*");
                }
                // catch (Exception) 
                catch (HalconException HDevExpDefaultException1)
                {
                    HDevExpDefaultException1.ToHTuple(out hv_Exception);
                    if ((int)((new HTuple(((hv_OS.TupleSubstr(0, 4))).TupleEqual("Linux"))).TupleAnd(
                        new HTuple(hv_Font_COPY_INP_TMP.TupleEqual("courier")))) != 0)
                    {
                        HOperatorSet.QueryFont(hv_WindowHandle, out hv_Fonts);
                        hv_FontSelRegexp = (("^-[^-]*-[^-]*[Cc]ourier[^-]*-" + hv_Bold_COPY_INP_TMP) + "-") + hv_Slant_COPY_INP_TMP;
                        hv_FontsCourier = ((hv_Fonts.TupleRegexpSelect(hv_FontSelRegexp))).TupleRegexpMatch(
                            hv_FontSelRegexp);
                        if ((int)(new HTuple((new HTuple(hv_FontsCourier.TupleLength())).TupleEqual(
                            0))) != 0)
                        {
                            hv_Exception = "Wrong font name";
                            //throw (Exception)
                        }
                        else
                        {
                            try
                            {
                                HOperatorSet.SetFont(hv_WindowHandle, (((hv_FontsCourier.TupleSelect(
                                    0)) + "-normal-*-") + hv_Size_COPY_INP_TMP) + "-*-*-*-*-*-*-*");
                            }
                            // catch (Exception) 
                            catch (HalconException HDevExpDefaultException2)
                            {
                                HDevExpDefaultException2.ToHTuple(out hv_Exception);
                                //throw (Exception)
                            }
                        }
                    }
                    //throw (Exception)
                }
            }
            // dev_set_preferences(...); only in hdevelop

            return;
        }
        public void disp_message(HTuple hv_WindowHandle, HTuple hv_String, HTuple hv_CoordSystem,
HTuple hv_Row, HTuple hv_Column, HTuple hv_Color, HTuple hv_Box)
        {

            HTuple hv_Red = null, hv_Green = null, hv_Blue = null;
            HTuple hv_Row1Part = null, hv_Column1Part = null, hv_Row2Part = null;
            HTuple hv_Column2Part = null, hv_RowWin = null, hv_ColumnWin = null;
            HTuple hv_WidthWin = null, hv_HeightWin = null, hv_MaxAscent = null;
            HTuple hv_MaxDescent = null, hv_MaxWidth = null, hv_MaxHeight = null;
            HTuple hv_R1 = new HTuple(), hv_C1 = new HTuple(), hv_FactorRow = new HTuple();
            HTuple hv_FactorColumn = new HTuple(), hv_UseShadow = null;
            HTuple hv_ShadowColor = null, hv_Exception = new HTuple();
            HTuple hv_Width = new HTuple(), hv_Index = new HTuple();
            HTuple hv_Ascent = new HTuple(), hv_Descent = new HTuple();
            HTuple hv_W = new HTuple(), hv_H = new HTuple(), hv_FrameHeight = new HTuple();
            HTuple hv_FrameWidth = new HTuple(), hv_R2 = new HTuple();
            HTuple hv_C2 = new HTuple(), hv_DrawMode = new HTuple();
            HTuple hv_CurrentColor = new HTuple();
            HTuple hv_Box_COPY_INP_TMP = hv_Box.Clone();
            HTuple hv_Color_COPY_INP_TMP = hv_Color.Clone();
            HTuple hv_Column_COPY_INP_TMP = hv_Column.Clone();
            HTuple hv_Row_COPY_INP_TMP = hv_Row.Clone();
            HTuple hv_String_COPY_INP_TMP = hv_String.Clone();

            //Prepare window
            HOperatorSet.GetRgb(hv_WindowHandle, out hv_Red, out hv_Green, out hv_Blue);
            HOperatorSet.GetPart(hv_WindowHandle, out hv_Row1Part, out hv_Column1Part, out hv_Row2Part,
                out hv_Column2Part);
            HOperatorSet.GetWindowExtents(hv_WindowHandle, out hv_RowWin, out hv_ColumnWin,
                out hv_WidthWin, out hv_HeightWin);
            HOperatorSet.SetPart(hv_WindowHandle, 0, 0, hv_HeightWin - 1, hv_WidthWin - 1);
            //
            //default settings
            if ((int)(new HTuple(hv_Row_COPY_INP_TMP.TupleEqual(-1))) != 0)
            {
                hv_Row_COPY_INP_TMP = 12;
            }
            if ((int)(new HTuple(hv_Column_COPY_INP_TMP.TupleEqual(-1))) != 0)
            {
                hv_Column_COPY_INP_TMP = 12;
            }
            if ((int)(new HTuple(hv_Color_COPY_INP_TMP.TupleEqual(new HTuple()))) != 0)
            {
                hv_Color_COPY_INP_TMP = "";
            }
            //
            hv_String_COPY_INP_TMP = ((("" + hv_String_COPY_INP_TMP) + "")).TupleSplit("\n");
            //
            //Estimate extentions of text depending on font size.
            HOperatorSet.GetFontExtents(hv_WindowHandle, out hv_MaxAscent, out hv_MaxDescent,
                out hv_MaxWidth, out hv_MaxHeight);
            if ((int)(new HTuple(hv_CoordSystem.TupleEqual("window"))) != 0)
            {
                hv_R1 = hv_Row_COPY_INP_TMP.Clone();
                hv_C1 = hv_Column_COPY_INP_TMP.Clone();
            }
            else
            {
                //Transform image to window coordinates
                hv_FactorRow = (1.0 * hv_HeightWin) / ((hv_Row2Part - hv_Row1Part) + 1);
                hv_FactorColumn = (1.0 * hv_WidthWin) / ((hv_Column2Part - hv_Column1Part) + 1);
                hv_R1 = ((hv_Row_COPY_INP_TMP - hv_Row1Part) + 0.5) * hv_FactorRow;
                hv_C1 = ((hv_Column_COPY_INP_TMP - hv_Column1Part) + 0.5) * hv_FactorColumn;
            }
            //
            //Display text box depending on text size
            hv_UseShadow = 1;
            hv_ShadowColor = "gray";
            if ((int)(new HTuple(((hv_Box_COPY_INP_TMP.TupleSelect(0))).TupleEqual("true"))) != 0)
            {
                if (hv_Box_COPY_INP_TMP == null)
                    hv_Box_COPY_INP_TMP = new HTuple();
                hv_Box_COPY_INP_TMP[0] = "#fce9d4";
                hv_ShadowColor = "#f28d26";
            }
            if ((int)(new HTuple((new HTuple(hv_Box_COPY_INP_TMP.TupleLength())).TupleGreater(
                1))) != 0)
            {
                if ((int)(new HTuple(((hv_Box_COPY_INP_TMP.TupleSelect(1))).TupleEqual("true"))) != 0)
                {
                    //Use default ShadowColor set above
                }
                else if ((int)(new HTuple(((hv_Box_COPY_INP_TMP.TupleSelect(1))).TupleEqual(
                    "false"))) != 0)
                {
                    hv_UseShadow = 0;
                }
                else
                {
                    hv_ShadowColor = hv_Box_COPY_INP_TMP[1];
                    //Valid color?
                    try
                    {
                        HOperatorSet.SetColor(hv_WindowHandle, hv_Box_COPY_INP_TMP.TupleSelect(
                            1));
                    }
                    // catch (Exception) 
                    catch (HalconException HDevExpDefaultException1)
                    {
                        HDevExpDefaultException1.ToHTuple(out hv_Exception);
                        hv_Exception = "Wrong value of control parameter Box[1] (must be a 'true', 'false', or a valid color string)";
                        throw new HalconException(hv_Exception);
                    }
                }
            }
            if ((int)(new HTuple(((hv_Box_COPY_INP_TMP.TupleSelect(0))).TupleNotEqual("false"))) != 0)
            {
                //Valid color?
                try
                {
                    HOperatorSet.SetColor(hv_WindowHandle, hv_Box_COPY_INP_TMP.TupleSelect(0));
                }
                // catch (Exception) 
                catch (HalconException HDevExpDefaultException1)
                {
                    HDevExpDefaultException1.ToHTuple(out hv_Exception);
                    hv_Exception = "Wrong value of control parameter Box[0] (must be a 'true', 'false', or a valid color string)";
                    throw new HalconException(hv_Exception);
                }
                //Calculate box extents
                hv_String_COPY_INP_TMP = (" " + hv_String_COPY_INP_TMP) + " ";
                hv_Width = new HTuple();
                for (hv_Index = 0; (int)hv_Index <= (int)((new HTuple(hv_String_COPY_INP_TMP.TupleLength()
                    )) - 1); hv_Index = (int)hv_Index + 1)
                {
                    HOperatorSet.GetStringExtents(hv_WindowHandle, hv_String_COPY_INP_TMP.TupleSelect(
                        hv_Index), out hv_Ascent, out hv_Descent, out hv_W, out hv_H);
                    hv_Width = hv_Width.TupleConcat(hv_W);
                }
                hv_FrameHeight = hv_MaxHeight * (new HTuple(hv_String_COPY_INP_TMP.TupleLength()
                    ));
                hv_FrameWidth = (((new HTuple(0)).TupleConcat(hv_Width))).TupleMax();
                hv_R2 = hv_R1 + hv_FrameHeight;
                hv_C2 = hv_C1 + hv_FrameWidth;
                //Display rectangles
                HOperatorSet.GetDraw(hv_WindowHandle, out hv_DrawMode);
                HOperatorSet.SetDraw(hv_WindowHandle, "fill");
                //Set shadow color
                HOperatorSet.SetColor(hv_WindowHandle, hv_ShadowColor);
                if ((int)(hv_UseShadow) != 0)
                {
                    HOperatorSet.DispRectangle1(hv_WindowHandle, hv_R1 + 1, hv_C1 + 1, hv_R2 + 1, hv_C2 + 1);
                }
                //Set box color
                HOperatorSet.SetColor(hv_WindowHandle, hv_Box_COPY_INP_TMP.TupleSelect(0));
                HOperatorSet.DispRectangle1(hv_WindowHandle, hv_R1, hv_C1, hv_R2, hv_C2);
                HOperatorSet.SetDraw(hv_WindowHandle, hv_DrawMode);
            }
            //Write text.
            for (hv_Index = 0; (int)hv_Index <= (int)((new HTuple(hv_String_COPY_INP_TMP.TupleLength()
                )) - 1); hv_Index = (int)hv_Index + 1)
            {
                hv_CurrentColor = hv_Color_COPY_INP_TMP.TupleSelect(hv_Index % (new HTuple(hv_Color_COPY_INP_TMP.TupleLength()
                    )));
                if ((int)((new HTuple(hv_CurrentColor.TupleNotEqual(""))).TupleAnd(new HTuple(hv_CurrentColor.TupleNotEqual(
                    "auto")))) != 0)
                {
                    HOperatorSet.SetColor(hv_WindowHandle, hv_CurrentColor);
                }
                else
                {
                    HOperatorSet.SetRgb(hv_WindowHandle, hv_Red, hv_Green, hv_Blue);
                }
                hv_Row_COPY_INP_TMP = hv_R1 + (hv_MaxHeight * hv_Index);
                HOperatorSet.SetTposition(hv_WindowHandle, hv_Row_COPY_INP_TMP, hv_C1);
                HOperatorSet.WriteString(hv_WindowHandle, hv_String_COPY_INP_TMP.TupleSelect(
                    hv_Index));
            }
            //Reset changed window settings
            HOperatorSet.SetRgb(hv_WindowHandle, hv_Red, hv_Green, hv_Blue);
            HOperatorSet.SetPart(hv_WindowHandle, hv_Row1Part, hv_Column1Part, hv_Row2Part,
                hv_Column2Part);

            return;
        }
        public double HtuplesTodouble(HTuple M)
        {
            double RM = new double();
            string QM = M[0];
            RM = Convert.ToDouble(QM);
            return RM;
        }
        public double HtupleTodouble(HTuple M)
        {
            double RM = new double();
          
            RM =(M[0]);
            return RM;
        }
        object obj=new object();
        public bool ReduceImage(HObject Image, HObject Region, out HObject RImage)
        {
            try
            {
                HOperatorSet.ReduceDomain(Image, Region, out RImage);
                return true;
            }
            catch
            {
                RImage = null;
                return false;
            }
        }
        public int CreateHoleModel(HObject Image, HObject region, HoleParamModel MP, out HTuple modelId)
        {

            HObject Rimage;
            HOperatorSet.GenEmptyObj(out Rimage);
            ReduceImage(Image, region, out Rimage);

            try
            {
                HOperatorSet.CreateShapeModel(Rimage, new HTuple((int)MP.numlevel), (new HTuple((int)MP.minangle)).TupleRad(), 
                    (new HTuple((int)MP.maxangle)).TupleRad(), 
                    (new HTuple(MP.angelstep)).TupleRad(), 
                    (new HTuple("none")).TupleConcat("no_pregeneration"),
                    "use_polarity", 
                    ((new HTuple((int)MP.highcontrast)).TupleConcat(new HTuple((int)MP.lowcontrast))).TupleConcat(new HTuple((int)MP.minsize)),
                    new HTuple((int)MP.mincontrast), 
                    out modelId);
                
            }
            catch
            {
                MessageBox.Show("特征过少，无法完成训练!");
                modelId = -1;
                return 0;
            }
            return 1;
        }
        public bool FindHoleModel(HObject Image, HWindow hv_hwindos, HoleParamModel MP, HTuple ModelId, out HTuple hv_Row, out HTuple hv_Column, out HTuple hv_Angle)
        {
            HObject ho_ModelContours, ho_TransContours = null;

            HTuple hv_Score = null, hv_MatchingObjIdx = null, hv_HomMat = new HTuple();
            HOperatorSet.GenEmptyObj(out ho_ModelContours);
            HOperatorSet.GenEmptyObj(out ho_TransContours);

            try
            {
                ho_ModelContours.Dispose();
                HOperatorSet.GetShapeModelContours(out ho_ModelContours, ModelId, 1);
                HOperatorSet.FindShapeModel(Image, 
                    ModelId,
                    ((HTuple)(int)MP.minangle).TupleRad(),
                    ((HTuple)(int)MP.minangle).TupleRad(),
                    (HTuple)MP.score,
                     1, 0.5, "least_squares",
                    (new HTuple(7)).TupleConcat(1), 
                    0.75, out hv_Row,
                    out hv_Column,
                    out hv_Angle,
                    out hv_Score);
               
                for (hv_MatchingObjIdx = 0; (int)hv_MatchingObjIdx <= (int)((new HTuple(hv_Score.TupleLength()
   )) - 1); hv_MatchingObjIdx = (int)hv_MatchingObjIdx + 1)
                {
                    HOperatorSet.HomMat2dIdentity(out hv_HomMat);
                    HOperatorSet.HomMat2dRotate(hv_HomMat, hv_Angle.TupleSelect(hv_MatchingObjIdx),
                        0, 0, out hv_HomMat);
                    HOperatorSet.HomMat2dTranslate(hv_HomMat, hv_Row.TupleSelect(hv_MatchingObjIdx),
                        hv_Column.TupleSelect(hv_MatchingObjIdx), out hv_HomMat);
                    ho_TransContours.Dispose();
                    HOperatorSet.AffineTransContourXld(ho_ModelContours, out ho_TransContours,
                        hv_HomMat);

                    HOperatorSet.SetColor(hv_hwindos, "orange");
                    HOperatorSet.DispObj(ho_TransContours, hv_hwindos);

                    HOperatorSet.SetColor(hv_hwindos, "green");
                    HObject cross;
                    HOperatorSet.GenCrossContourXld(out cross, hv_Row.TupleSelect(hv_MatchingObjIdx),
                        hv_Column.TupleSelect(hv_MatchingObjIdx), new HTuple(30), 0);
                    HOperatorSet.DispObj(cross, hv_hwindos);



                    HOperatorSet.SetColor(hv_hwindos, "blue");
                    set_display_font(hv_hwindos, 25, "sans", "true", "false");

                    //disp_message(hv_hwindos, hv_MatchingObjIdx + 1, "window", 0, 0, "green", "true");
                    HTuple row = hv_Row.TupleSelect(hv_MatchingObjIdx).D.ToString("0.000");
                    HTuple col = hv_Column.TupleSelect(hv_MatchingObjIdx).D.ToString("0.000");
                    HTuple angle = hv_Angle.TupleSelect(hv_MatchingObjIdx).D.ToString("0.000");
                    disp_message(hv_hwindos, row + ", " + col + ", " + angle + ", " +
                       hv_Score.TupleSelect(hv_MatchingObjIdx), "window", 0, 0, "green", "true");


                }

                ho_ModelContours.Dispose();
                ho_TransContours.Dispose();
                if (hv_Score > MP.score)
                {
                    return true;
                }
                else
                {
                    return false;
                }

            }
            catch
            {
                hv_Row = null;
                hv_Column = null;
                hv_Angle = null;
                ho_ModelContours.Dispose();
                ho_TransContours.Dispose();
                return false;
            }

        }
        public bool FindErModel(HObject ho_Image, ErModelParam erModelParam,
            out HTuple row, out HTuple col)
        {
            HObject  ho_ImageGauss = null;
            HObject ho_ImageReduced = null, ho_Region = null, ho_RegionFillUp = null;
            HObject ho_Rectangle1 = null, ho_Rectangle3 = null, ho_RegionDifference = null;
            HObject ho_ConnectedRegions1 = null;
            HObject ho_SelectedRegions = null, ho_RegionUnion = null, ho_ConnectedRegions = null;
            HObject SelectedRegionsstd=null, SelectedRegionsarea = null;

            HTuple hv_UsedThreshold = new HTuple(), hv_Row1 = new HTuple();
            HTuple hv_Column1 = new HTuple(), hv_Row2 = new HTuple();
            HTuple hv_Column2 = new HTuple(), hv_Area = new HTuple();
            HTuple hv_Row4 = new HTuple(), hv_Column4 = new HTuple();
            HTuple hv_Row3 = new HTuple(), hv_Column3 = new HTuple();
            HTuple hv_Radius = new HTuple(), hv_Width = new HTuple();
            HTuple hv_Height = new HTuple(), hv_Angle = new HTuple();

            HTuple hv_areastd=new HTuple(),hv_rowstd=new HTuple(),hv_colstd=new HTuple();
            HTuple hv_areaCK=new HTuple(),hv_rowCK= new HTuple(),hv_colCK=new HTuple();


            HOperatorSet.GenEmptyObj(out ho_ImageGauss);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Region);
            HOperatorSet.GenEmptyObj(out ho_ConnectedRegions);
            HOperatorSet.GenEmptyObj(out ho_SelectedRegions);
            HOperatorSet.GenEmptyObj(out ho_RegionUnion);
            HOperatorSet.GenEmptyObj(out ho_RegionFillUp);
            HOperatorSet.GenEmptyObj(out ho_Rectangle1);
            HOperatorSet.GenEmptyObj(out ho_RegionDifference);
            HOperatorSet.GenEmptyObj(out ho_ConnectedRegions1);
            HOperatorSet.GenEmptyObj(out ho_Rectangle3);
            HOperatorSet.GenEmptyObj(out SelectedRegionsstd);
            HOperatorSet.GenEmptyObj(out SelectedRegionsarea);
            
            try
            {
                ho_ImageGauss.Dispose();
                HOperatorSet.GaussFilter(ho_Image, out ho_ImageGauss, 11);//高斯预处理

                ho_ImageReduced.Dispose();
                HOperatorSet.ReduceDomain(ho_ImageGauss, erModelParam.hmregion, out ho_ImageReduced);//region加载再图片上

                ho_Region.Dispose();
                HOperatorSet.BinaryThreshold(ho_ImageReduced, out ho_Region, "smooth_histo",
                    "dark", out hv_UsedThreshold);//二值化

                ho_ConnectedRegions.Dispose();
                HOperatorSet.Connection(ho_Region, out ho_ConnectedRegions);//分割

                ho_SelectedRegions.Dispose();
                HOperatorSet.SelectShape(ho_ConnectedRegions, out ho_SelectedRegions, "area",
                    "and", 2000, 99999);//面积查找

                ho_RegionUnion.Dispose();
                HOperatorSet.Union1(ho_SelectedRegions, out ho_RegionUnion);//合并

                ho_RegionFillUp.Dispose();
                HOperatorSet.FillUp(ho_RegionUnion, out ho_RegionFillUp);//填充

                HOperatorSet.SmallestRectangle1(ho_RegionFillUp, out hv_Row1, out hv_Column1,
                      out hv_Row2, out hv_Column2);//最小外接矩形

                ho_Rectangle1.Dispose();
                HOperatorSet.GenRectangle1(out ho_Rectangle1, hv_Row1, hv_Column1, hv_Row2,
                    hv_Column2);//绘制最小外接矩形

                HOperatorSet.AreaCenter(ho_Rectangle1, out hv_Area, out hv_Row4, out hv_Column4);//获取面积和坐标

                ho_Rectangle3.Dispose();
                HOperatorSet.GenRectangle2(out ho_Rectangle3, hv_Row4, hv_Column4, 0, (HTuple)erModelParam.length1,
                    (HTuple)erModelParam.length2);//绘制可变矩形

                ho_RegionDifference.Dispose();
                HOperatorSet.Difference(ho_RegionUnion, ho_Rectangle3, out ho_RegionDifference);//region相减

                ho_ConnectedRegions1.Dispose();
                HOperatorSet.Connection(ho_RegionDifference, out ho_ConnectedRegions1);//分割

                HOperatorSet.AreaCenter(ho_ConnectedRegions1, out hv_areaCK, out hv_rowCK, out hv_colCK);
               
                if (hv_areaCK.Length > 2)//判断是否有超过2个
                {
                    HTuple sortArea = new HTuple();
                    HOperatorSet.TupleSort(hv_areaCK,out sortArea);//对面积进行从小到大排序
                    if (sortArea[hv_areaCK.Length - 1] >= 10 && sortArea[hv_areaCK.Length] >= 10)//判断找到的形状是否超过2000，
                    {
                        SelectedRegionsarea.Dispose();
                        HOperatorSet.SelectShape(ho_ConnectedRegions1, out SelectedRegionsarea, "area", "and",
                            sortArea[hv_areaCK.Length - 1] - 1, sortArea[hv_areaCK.Length] + 1);//取最后两个

                        HOperatorSet.SmallestCircle(SelectedRegionsarea, out hv_Row3, out hv_Column3,
                            out hv_Radius);//做最小内接圆

                        row = hv_Row3;
                        col = hv_Column3;

                        ho_Image.Dispose();
                        ho_ImageGauss.Dispose();
                        ho_ImageReduced.Dispose();
                        ho_Region.Dispose();
                        ho_RegionFillUp.Dispose();
                        ho_Rectangle1.Dispose();
                        ho_RegionDifference.Dispose();
                        ho_Rectangle3.Dispose();
                        ho_ConnectedRegions1.Dispose();
                        SelectedRegionsarea.Dispose();
                        SelectedRegionsstd.Dispose();
                        if (hv_Row3.Length >= 2 && hv_Column3.Length >= 2)
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else
                    {
                        ho_Image.Dispose();
                        ho_ImageGauss.Dispose();
                        ho_ImageReduced.Dispose();
                        ho_Region.Dispose();
                        ho_RegionFillUp.Dispose();
                        ho_Rectangle1.Dispose();
                        ho_RegionDifference.Dispose();
                        ho_Rectangle3.Dispose();
                        ho_ConnectedRegions1.Dispose();
                        SelectedRegionsarea.Dispose();
                        SelectedRegionsstd.Dispose();
                        row = null;
                        col = null;
                        return false;
                    }
                }
                else
                {
                    ho_Image.Dispose();
                    ho_ImageGauss.Dispose();
                    ho_ImageReduced.Dispose();
                    ho_Region.Dispose();
                    ho_RegionFillUp.Dispose();
                    ho_Rectangle1.Dispose();
                    ho_RegionDifference.Dispose();
                    ho_Rectangle3.Dispose();
                    ho_ConnectedRegions1.Dispose();
                    SelectedRegionsarea.Dispose();
                    SelectedRegionsstd.Dispose();
                    row = null;
                    col = null;
                    return false;
                }


            }
            catch
            {
                ho_Image.Dispose();
                ho_ImageGauss.Dispose();
                ho_ImageReduced.Dispose();
                ho_Region.Dispose();
                ho_RegionFillUp.Dispose();
                ho_Rectangle1.Dispose();
                ho_RegionDifference.Dispose();
                ho_Rectangle3.Dispose();
                ho_ConnectedRegions1.Dispose();
                SelectedRegionsarea.Dispose();
                SelectedRegionsstd.Dispose();
                row = null;
                col = null;
                return false;
            }

        }
        public bool CalcOfLLAngle(HObject ho_Image, HoleParamModel HpM, HWindow hv_hwindos, out HTuple angle)
        {
            HTuple hv_Row, hv_Column, hv_Angle, hv_Score;
            HTuple HomMat2D;

            HObject ho_Rectangle = null, ho_Rectangle1 = null;
            HObject ho_Rectangle2 = null, ho_Rectangle3 = null;
            HObject ho_ModelContours,  ho_ImageReduced = null;
            HObject ho_Region = null, ho_RegionLines = null, ho_ImageReduced1 = null;
            HObject ho_Region1 = null, ho_RegionLines1 = null;

            HTuple hv_UsedThreshold = new HTuple(), hv_Row2 = new HTuple();
            HTuple hv_Column2 = new HTuple(), hv_Phi1 = new HTuple();
            HTuple hv_Length11 = new HTuple(), hv_Length21 = new HTuple();
            HTuple hv_RowBegin = new HTuple(), hv_ColBegin = new HTuple();
            HTuple hv_RowEnd = new HTuple(), hv_ColEnd = new HTuple();
            HTuple hv_UsedThreshold1 = new HTuple(), hv_Row3 = new HTuple();
            HTuple hv_Column3 = new HTuple(), hv_Phi3 = new HTuple();
            HTuple hv_Length13 = new HTuple(), hv_Length23 = new HTuple();
            HTuple hv_RowBegin1 = new HTuple(), hv_ColBegin1 = new HTuple();
            HTuple hv_RowEnd1 = new HTuple(), hv_ColEnd1 = new HTuple();
            HTuple hv_Angle1 = new HTuple(), hv_am = new HTuple();

            HOperatorSet.GenEmptyObj(out ho_Rectangle);
            HOperatorSet.GenEmptyObj(out ho_Rectangle1);
            HOperatorSet.GenEmptyObj(out ho_Rectangle2);
            HOperatorSet.GenEmptyObj(out ho_Rectangle3);
            HOperatorSet.GenEmptyObj(out ho_ModelContours);            
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Region);
            HOperatorSet.GenEmptyObj(out ho_RegionLines);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced1);
            HOperatorSet.GenEmptyObj(out ho_Region1);
            HOperatorSet.GenEmptyObj(out ho_RegionLines1);

            try
            {

                HOperatorSet.FindShapeModel(ho_Image,
                  HpM.Modelparam.ModelId ,
                   ((HTuple)(int)HpM.minangle).TupleRad(),
                   ((HTuple)(int)HpM.minangle).TupleRad(),
                   (HTuple)HpM.score,
                    1, 0.5, "least_squares",
                   (new HTuple(7)).TupleConcat(1),
                   0.75, out hv_Row,
                   out hv_Column,
                   out hv_Angle,
                   out hv_Score);

                HOperatorSet.VectorAngleToRigid(((HTuple)(double)HpM.Modelparam.Cmx), ((HTuple)(double)HpM.Modelparam.Cmy), 0,
                    hv_Row, hv_Column, hv_Angle, out HomMat2D);



                ho_Rectangle2.Dispose();
                HOperatorSet.GenRectangle2(out ho_Rectangle2, ((HTuple)(double)HpM.ampm.r2_1x), ((HTuple)(double)HpM.ampm.r2_1y),
                   ((HTuple)(double)HpM.ampm.r2_1a), ((HTuple)(double)HpM.ampm.r2_1l1), ((HTuple)(double)HpM.ampm.r2_1l2));

                ho_Rectangle3.Dispose();
                HOperatorSet.GenRectangle2(out ho_Rectangle3, ((HTuple)(double)HpM.ampm.r2_2x), ((HTuple)(double)HpM.ampm.r2_2y),
                   ((HTuple)(double)HpM.ampm.r2_2a), ((HTuple)(double)HpM.ampm.r2_2l1), ((HTuple)(double)HpM.ampm.r2_2l2));

                ho_Rectangle.Dispose();
                HOperatorSet.AffineTransRegion(ho_Rectangle3, out ho_Rectangle, HomMat2D,
                    "nearest_neighbor");
                ho_Rectangle1.Dispose();
                HOperatorSet.AffineTransRegion(ho_Rectangle2, out ho_Rectangle1, HomMat2D,
                    "nearest_neighbor");

                ho_ImageReduced.Dispose();
            HOperatorSet.ReduceDomain(ho_Image, ho_Rectangle1, out ho_ImageReduced);

            ho_Region.Dispose();
            HOperatorSet.BinaryThreshold(ho_ImageReduced, out ho_Region, "max_separability",
                "dark", out hv_UsedThreshold);

            HOperatorSet.SmallestRectangle2(ho_Region, out hv_Row2, out hv_Column2, out hv_Phi1,
                out hv_Length11, out hv_Length21);
            hv_RowBegin = hv_Row2 - (hv_Length11 * (hv_Phi1.TupleSin()));
            hv_ColBegin = hv_Column2 + (hv_Length11 * (hv_Phi1.TupleCos()));
            hv_RowEnd = hv_Row2 + (hv_Length11 * (hv_Phi1.TupleSin()));
            hv_ColEnd = hv_Column2 - (hv_Length11 * (hv_Phi1.TupleCos()));

            ho_RegionLines.Dispose();
            HOperatorSet.GenRegionLine(out ho_RegionLines, hv_RowBegin, hv_ColBegin,
                hv_RowEnd, hv_ColEnd);

            HOperatorSet.DispObj(ho_RegionLines, hv_hwindos);
            ho_ImageReduced1.Dispose();
            HOperatorSet.ReduceDomain(ho_Image, ho_Rectangle, out ho_ImageReduced1);
            ho_Region1.Dispose();
            HOperatorSet.BinaryThreshold(ho_ImageReduced1, out ho_Region1, "max_separability",
                "dark", out hv_UsedThreshold1);

            HOperatorSet.SmallestRectangle2(ho_Region1, out hv_Row3, out hv_Column3,
                out hv_Phi3, out hv_Length13, out hv_Length23);
            hv_RowBegin1 = hv_Row3 - (hv_Length13 * (hv_Phi3.TupleSin()));
            hv_ColBegin1 = hv_Column3 + (hv_Length13 * (hv_Phi3.TupleCos()));
            hv_RowEnd1 = hv_Row3 + (hv_Length13 * (hv_Phi3.TupleSin()));
            hv_ColEnd1 = hv_Column3 - (hv_Length13 * (hv_Phi3.TupleCos()));

            ho_RegionLines1.Dispose();
            HOperatorSet.GenRegionLine(out ho_RegionLines1, hv_RowBegin1, hv_ColBegin1,
                hv_RowEnd1, hv_ColEnd1);
             HOperatorSet.DispObj(ho_RegionLines1, hv_hwindos);
             HOperatorSet.AngleLl(hv_RowBegin, hv_ColBegin, hv_RowEnd, hv_ColEnd, hv_RowBegin1,
                hv_ColBegin1, hv_RowEnd1, hv_ColEnd1, out hv_Angle1);

                angle = hv_Angle1.TupleDeg();
                ho_Rectangle.Dispose();
                ho_Rectangle1.Dispose();
                ho_ModelContours.Dispose();
                ho_Image.Dispose();
                ho_ImageReduced.Dispose();
                ho_Region.Dispose();
                ho_RegionLines.Dispose();
                ho_ImageReduced1.Dispose();
                ho_Region1.Dispose();
                ho_RegionLines1.Dispose();
                return true;
            }
            catch 
            {
                ho_Rectangle.Dispose();
                ho_Rectangle1.Dispose();
                ho_ModelContours.Dispose();
                ho_Image.Dispose();
                ho_ImageReduced.Dispose();
                ho_Region.Dispose();
                ho_RegionLines.Dispose();
                ho_ImageReduced1.Dispose();
                ho_Region1.Dispose();
                ho_RegionLines1.Dispose();
                angle = 0;               
               
                return false;
            }            
        }
        
    }
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace 磨边机视觉定位系统
{
    public partial class Form2 : Form
    {
        private Form1 frm;
        private int State = 0;
        public Form2()
        {
            InitializeComponent();
        }

        public void Init(Form1 _Prmfrm, string _Name, string _Address, int _Type, string _Num, string _Desc, int _Connect, int _State)
        {
            frm = _Prmfrm;
            comboBox1.Items.Clear();
            comboBox3.Items.Clear();
            for (int i = 0; i < frm.Pcs.StyleNum; i++)
            {
                this.comboBox1.Items.Add(Enum.Parse(typeof(Style), i.ToString()));
            }

            for (int i = 0; i < frm.Pcs.ConnectNum; i++)
            {
                this.comboBox3.Items.Add(Enum.Parse(typeof(Connect), i.ToString()));
            }

            State = _State;
            textBox1.Text = _Name;
            textBox2.Text = _Address;
            comboBox1.SelectedIndex = Convert.ToInt16(_Type);
            comboBox3.SelectedIndex = _Connect;
            textBox3.Text = _Num;
            textBox4.Text = _Desc;
        }
        private void button1_Click(object sender, EventArgs e)
        {
            DialogResult dr = MessageBox.Show("是否保存设置？", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
            if (dr == DialogResult.OK)
            {
                string Name = textBox1.Text;
                string Address = textBox2.Text;
                int Type = comboBox1.SelectedIndex;
                string Num = textBox3.Text;
                string Desc = textBox4.Text;
                int Connect = comboBox3.SelectedIndex;
                frm.AddDataview(Name, Address, Type, Num, Desc, Connect, State);
            }

            this.Close();
        }
        private void Form2_Load(object sender, EventArgs e)
        {

        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}

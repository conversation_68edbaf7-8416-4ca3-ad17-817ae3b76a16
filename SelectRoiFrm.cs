﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace 视觉检测系统
{
    public partial class SelectRoiFrm : Form
    {
        public SelectRoiFrm()
        {
            InitializeComponent();
            if (GetPoints()!=null)
            {
                this.label2.Text = GetPoints();
            }
            else
            {
                this.label2.Text = "未设置";
            }
        }
        private readonly static IniFileUtils iniFileUtil = new IniFileUtils(Environment.CurrentDirectory + "\\AppConfig.ini");
        System.Drawing.Point RectstartPoint;//获取鼠标按下时的坐标
        System.Drawing.Rectangle Rect;      //重绘区域
        bool blnDraw = false;               //重新绘制感兴趣区域标志
        Bitmap mapHist;                     //存储数据的位图
        int[] countpixel;                   //存储每个直方图的数据
        int maxPixel;
        Mat ImageROI;
        Mat img;
        System.Drawing.Rectangle RealImageRect;

        private string GetPoints()
        {
            string point1 = iniFileUtil.ReadString("roi", "point1", "");
            string point2 = iniFileUtil.ReadString("roi", "point1", "");
            string point3 = iniFileUtil.ReadString("roi", "point1", "");
            string point4 = iniFileUtil.ReadString("roi", "point1", "");

            if(point1 != "" && point2 != "" && point3 != "" && point4 != "")
            {
                return point1 + "," + point2 + "," + point3 + "," + point4;
            }
            return null;
        }
        private void button1_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "图文件(*.*)|*.jpg;*.png;*.jpeg;*.bmp";
            openFileDialog.RestoreDirectory = true;
            openFileDialog.Multiselect = false;

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                return;


            }
            textBox1.Text = openFileDialog.FileName;

            // 使用OpenCvSharp读取图像
            //using (var image = new OpenCvSharp.Mat(openFileDialog.FileName))
            //{
            //    //// 调整图像大小以匹配pictureBox1的大小
            //    //var newSize = new OpenCvSharp.Size(pictureBox1.Width, pictureBox1.Height);
            //    //Cv2.Resize(image, image, newSize);
               
            //    // 将OpenCvSharp的Mat转换为Bitmap
            //    //pictureBox1.Image = OpenCvSharp.Extensions.Converters MatToBitmapConverter(image);
            //}
            pictureBox1.Image = new Bitmap(openFileDialog.FileName) as Image;
            img = Cv2.ImRead(openFileDialog.FileName);
        }

        private void pictureBox1_MouseDown(object sender, MouseEventArgs e)
        {
            RectstartPoint = e.Location;
            Invalidate();
            blnDraw = true;
        }

        private void pictureBox1_MouseMove(object sender, MouseEventArgs e)
        {
            if (blnDraw)
            {
                if (e.Button != MouseButtons.Left)//如果不是鼠标左键按下
                {
                    return;
                }
                System.Drawing.Point tempendPoint = e.Location;//记录框的位置和大小
                //pictureBox1上开始点的坐标

                Rect.Location = new System.Drawing.Point(
                    Math.Min(RectstartPoint.X, tempendPoint.X),
                    Math.Min(RectstartPoint.Y, tempendPoint.Y));
                //picture上矩形的大小
                Rect.Size = new System.Drawing.Size(
                    Math.Abs(RectstartPoint.X - tempendPoint.X),
                    Math.Abs(RectstartPoint.Y - tempendPoint.Y));
                pictureBox1.Invalidate();
                //最后点的位置
                int X0, Y0;
                Utilities.ConvertCoordinates(pictureBox1, out X0, out Y0, e.X, e.Y);
                //在控件中
                //textBox1.Text = Convert.ToString("picturebox最后的点坐标为" + e.X + "," + e.Y);
                //textBox2.Text = Convert.ToString("picturebox开始点的坐标为" + RectstartPoint.X + "," + RectstartPoint.Y);
                //textBox3.Text = Convert.ToString("picturebox的大小为" + Rect.X + "," + Rect.Y);
                //Create ROI感兴趣区域
                Utilities.ConvertCoordinates(pictureBox1, out X0, out Y0, RectstartPoint.X, RectstartPoint.Y);
                int X1, Y1;
                Utilities.ConvertCoordinates(pictureBox1, out X1, out Y1, tempendPoint.X, tempendPoint.Y);
                //感兴趣区域 左上角坐标-宽-高
                RealImageRect.Location = new System.Drawing.Point(Math.Min(X0, X1), Math.Min(Y0, Y1));
                RealImageRect.Size = new System.Drawing.Size(Math.Abs(X0 - X1), Math.Abs(Y0 - Y1));

                Rect tmp_rect = new Rect(RealImageRect.X, RealImageRect.Y, RealImageRect.Width, RealImageRect.Height);
                ImageROI = new Mat(img, tmp_rect);
            }
        }

        private void pictureBox1_MouseUp(object sender, MouseEventArgs e)
        {
            //pictureBox2.Image = ImageROI.ToBitmap();
            blnDraw = false;
        }

        private void pictureBox1_Paint(object sender, PaintEventArgs e)
        {
            if (blnDraw)
            {
                if (pictureBox1.Image != null)
                {
                    if (Rect != null && Rect.Width > 0 && Rect.Height > 0)
                    {
                        e.Graphics.DrawRectangle(new Pen(Color.Red, 1), Rect);//重新绘制的颜色为红色
                    }
                }
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            label2.Text = $"({RealImageRect.X},{RealImageRect.Y}),({RealImageRect.X+ RealImageRect.Width},{RealImageRect.Y}),({RealImageRect.X+ RealImageRect.Width},{RealImageRect.Y+ RealImageRect.Height}),({RealImageRect.X},{RealImageRect.Y+ RealImageRect.Height})";
            //写入配置文件
            try
            {
                iniFileUtil.WriteString("roi", "point1", $"({RealImageRect.X},{RealImageRect.Y})");
                iniFileUtil.WriteString("roi", "point2", $"({RealImageRect.X + RealImageRect.Width},{RealImageRect.Y})");
                iniFileUtil.WriteString("roi", "point3", $"({RealImageRect.X + RealImageRect.Width},{RealImageRect.Y + RealImageRect.Height})");
                iniFileUtil.WriteString("roi", "point4", $"({RealImageRect.X},{RealImageRect.Y + RealImageRect.Height})");
                MessageBox.Show("写入配置文件成功");
            }
            catch (Exception ex) { 
                MessageBox.Show("写入配置文件失败：" + ex.Message);
            }

         

        }
    }
}

﻿using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 磨边机视觉定位系统
{
    public class DbHelper
    {
        public static IDbConnection GetDbConnection()
        {
            var con = new MySqlConnection(GetMySqlConnectionString());
            con.Open();
            return con;
        }

        public static string GetMySqlConnectionString(string connectionName = "MySqlData")
        {
            var result = ConfigurationManager.ConnectionStrings[connectionName].ToString();
            if (string.IsNullOrWhiteSpace(result))
            {
                throw new Exception("数据库连接字符串配置错误!");
            }
            return result;
        }
    }
}

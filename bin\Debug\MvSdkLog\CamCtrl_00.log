[2020-01-02 16:34:02:0778] | CInterfaceManager.cpp-L4755: Cam<PERSON><PERSON><PERSON>[2040003], <PERSON><PERSON><PERSON>K<PERSON>(2040003), <PERSON><PERSON><PERSON><PERSON>(2040003)
[2020-01-02 16:34:05:0092] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 16:34:05:0139] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2824]
[2020-01-02 16:34:05:0139] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10460]
[2020-01-02 16:34:05:0383] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 16:34:08:0763] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 16:34:08:0775] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 16:34:11:0615] | CInterfaceManager.cpp-L4755: Cam<PERSON>trl[2040003], <PERSON>igE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 16:34:13:0573] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 16:34:13:0621] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6080]
[2020-01-02 16:34:13:0621] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2136]
[2020-01-02 16:34:13:0813] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 16:34:14:0905] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 16:34:14:0917] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 16:35:32:0623] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 16:36:05:0263] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 16:38:18:0471] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 16:38:19:0883] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 16:38:19:0929] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[916]
[2020-01-02 16:38:19:0929] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9360]
[2020-01-02 16:38:20:0182] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 16:38:25:0784] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 16:38:26:0543] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 16:38:26:0557] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 16:41:07:0612] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 16:41:08:0952] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 16:41:08:0999] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7412]
[2020-01-02 16:41:08:0999] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10848]
[2020-01-02 16:41:09:0284] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 16:41:21:0517] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 16:41:21:0530] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 17:00:45:0502] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 17:00:47:0165] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 17:00:47:0212] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1312]
[2020-01-02 17:00:47:0212] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11200]
[2020-01-02 17:00:47:0435] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 17:00:50:0491] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 17:00:52:0523] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 17:00:52:0536] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 17:02:34:0996] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 17:03:16:0258] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 17:03:16:0306] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9688]
[2020-01-02 17:03:16:0306] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10504]
[2020-01-02 17:03:17:0311] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 17:03:47:0839] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 17:03:50:0247] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 17:03:50:0321] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 17:05:11:0283] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 17:05:14:0620] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 17:05:14:0666] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9388]
[2020-01-02 17:05:14:0666] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8460]
[2020-01-02 17:05:14:0940] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 17:05:20:0030] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 17:05:20:0045] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 17:05:55:0072] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 17:05:57:0655] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 17:05:57:0702] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10636]
[2020-01-02 17:05:57:0703] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3024]
[2020-01-02 17:05:57:0930] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 17:06:06:0232] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 17:06:12:0536] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 17:06:13:0341] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 17:06:13:0406] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-02 17:48:53:0451] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-02 17:48:55:0403] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-02 17:48:55:0449] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1672]
[2020-01-02 17:48:55:0449] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10420]
[2020-01-02 17:48:55:0601] |         MvRender.cpp-L0093: RenderMode GDI
[2020-01-02 17:49:16:0430] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 17:49:24:0839] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-02 17:49:33:0542] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-02 17:49:33:0544] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 09:28:03:0989] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:29:56:0422] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:34:00:0354] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:50:39:0594] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:51:10:0951] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:51:24:0706] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:53:31:0919] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:54:01:0642] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:54:14:0836] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:54:44:0145] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:57:23:0173] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 09:57:46:0418] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:14:27:0250] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:15:00:0904] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:39:59:0140] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:40:21:0463] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:40:24:0222] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:40:24:0261] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7860]
[2020-01-03 10:40:24:0261] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9020]
[2020-01-03 10:40:32:0710] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:40:36:0491] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:40:37:0559] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:40:44:0254] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:40:45:0577] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:40:47:0327] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:40:54:0440] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:41:01:0485] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:41:06:0332] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:41:32:0749] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:41:32:0765] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:44:40:0724] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:44:42:0477] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:44:42:0521] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8288]
[2020-01-03 10:44:42:0521] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6464]
[2020-01-03 10:44:47:0502] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:44:48:0534] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:45:00:0617] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:45:11:0784] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:45:11:0823] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:45:16:0054] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:45:21:0129] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:46:16:0277] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:46:18:0367] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:46:18:0412] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3740]
[2020-01-03 10:46:18:0412] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6240]
[2020-01-03 10:46:23:0038] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:46:24:0054] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:46:25:0970] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:46:44:0837] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 10:46:50:0714] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:46:57:0062] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:46:57:0092] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:47:01:0270] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:47:03:0476] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:47:03:0520] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5488]
[2020-01-03 10:47:03:0520] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7848]
[2020-01-03 10:47:08:0142] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:47:09:0490] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:47:09:0521] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:48:31:0344] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:48:33:0420] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:48:33:0465] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7324]
[2020-01-03 10:48:33:0465] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1608]
[2020-01-03 10:48:43:0882] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:48:48:0543] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:48:58:0104] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:49:01:0536] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:49:04:0092] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:49:04:0166] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:51:17:0795] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:51:26:0613] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:51:26:0657] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[772]
[2020-01-03 10:51:26:0657] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8124]
[2020-01-03 10:51:28:0610] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:51:56:0418] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:51:59:0696] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:02:0368] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:07:0348] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:10:0378] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:11:0247] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:52:11:0259] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:52:33:0826] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:52:35:0732] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:52:35:0777] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7860]
[2020-01-03 10:52:35:0777] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4592]
[2020-01-03 10:52:38:0617] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:40:0183] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:42:0024] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:52:43:0170] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:52:43:0178] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 10:53:53:0995] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 10:53:55:0419] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 10:53:55:0464] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7896]
[2020-01-03 10:53:55:0465] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7280]
[2020-01-03 10:54:00:0564] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:01:0735] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:04:0048] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:05:0322] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:09:0933] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:11:0025] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:12:0543] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:13:0288] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 10:54:14:0016] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 10:54:14:0066] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:08:55:0115] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:08:56:0932] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:08:56:0975] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7540]
[2020-01-03 11:08:56:0976] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7916]
[2020-01-03 11:09:04:0476] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:08:0105] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:14:0557] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:19:0073] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:21:0007] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:22:0794] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:29:0745] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:31:0046] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:09:33:0798] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:09:33:0878] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:11:08:0152] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:11:10:0289] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:11:10:0334] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8256]
[2020-01-03 11:11:10:0334] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[876]
[2020-01-03 11:11:10:0369] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:12:0221] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:15:0050] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:17:0195] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:19:0366] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:21:0804] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:38:0322] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:11:38:0336] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:11:43:0433] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:11:46:0040] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:11:46:0083] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3060]
[2020-01-03 11:11:46:0083] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8264]
[2020-01-03 11:11:48:0951] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:50:0334] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 11:11:51:0601] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:52:0869] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 11:11:53:0464] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:11:56:0958] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:11:56:0984] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:16:08:0274] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:16:09:0860] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:16:09:0904] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6944]
[2020-01-03 11:16:09:0904] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9084]
[2020-01-03 11:16:12:0832] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:14:0283] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 11:16:15:0080] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:16:0553] |         XmlParse.cpp-L2871: SetNodeValue [TriggerSoftware], [Node is not writable. : AccessException thrown in node 'TriggerSoftware' while calling 'TriggerSoftware.Execute()' (file 'CommandT.h', line 61)]
[2020-01-03 11:16:17:0230] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:20:0817] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:22:0037] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:16:22:0105] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:16:28:0281] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:16:30:0024] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:16:30:0069] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8424]
[2020-01-03 11:16:30:0069] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6304]
[2020-01-03 11:16:30:0102] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:31:0860] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:32:0979] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:35:0425] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:36:0348] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:37:0101] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:37:0796] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:38:0439] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:39:0301] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:16:39:0969] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:16:39:0974] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:22:17:0815] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:22:21:0152] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:22:21:0198] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6300]
[2020-01-03 11:22:21:0199] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3928]
[2020-01-03 11:22:21:0233] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:22:24:0299] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:22:24:0304] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:22:32:0861] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:22:34:0283] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:22:34:0328] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10092]
[2020-01-03 11:22:34:0329] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1724]
[2020-01-03 11:22:38:0263] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:22:41:0999] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:22:45:0547] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:22:46:0087] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:22:46:0130] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:35:52:0281] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:35:54:0433] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:35:54:0478] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2100]
[2020-01-03 11:35:54:0478] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9424]
[2020-01-03 11:35:54:0512] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:35:59:0941] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:01:0702] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:07:0152] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:11:0855] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:12:0991] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:36:13:0079] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:36:19:0887] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:36:22:0046] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:36:22:0091] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7488]
[2020-01-03 11:36:22:0091] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9276]
[2020-01-03 11:36:24:0986] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:28:0146] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:33:0485] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:36:34:0256] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:36:34:0291] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:38:12:0602] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:38:13:0949] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:38:13:0994] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7860]
[2020-01-03 11:38:13:0994] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2896]
[2020-01-03 11:38:15:0789] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:17:0007] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:18:0653] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:19:0885] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:20:0821] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:21:0750] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:24:0111] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:26:0267] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:27:0664] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:30:0921] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:32:0519] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:38:33:0159] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:38:33:0195] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:41:50:0581] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:41:52:0290] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:41:52:0335] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7104]
[2020-01-03 11:41:52:0336] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6996]
[2020-01-03 11:41:54:0703] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:41:58:0693] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:02:0859] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:05:0188] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:14:0351] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:24:0992] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:32:0420] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:42:0924] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:42:43:0575] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:42:43:0639] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:43:01:0989] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:43:32:0411] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:43:35:0902] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:43:35:0948] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7304]
[2020-01-03 11:43:35:0949] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10076]
[2020-01-03 11:43:43:0216] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:44:12:0852] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:45:24:0560] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:45:25:0243] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:45:25:0256] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:46:02:0283] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:46:05:0697] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:46:05:0741] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4144]
[2020-01-03 11:46:05:0741] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9236]
[2020-01-03 11:46:12:0958] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:22:0638] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:30:0211] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:35:0154] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:36:0383] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:46:36:0443] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:46:48:0809] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:46:50:0412] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:46:50:0457] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8548]
[2020-01-03 11:46:50:0457] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9960]
[2020-01-03 11:46:52:0524] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:53:0781] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:55:0781] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:57:0052] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:46:58:0049] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:00:0470] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:04:0629] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:06:0062] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:10:0278] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:11:0120] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:12:0743] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:47:13:0971] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:47:14:0058] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:48:12:0882] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:48:21:0526] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:48:23:0653] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:48:23:0697] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1196]
[2020-01-03 11:48:23:0697] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6156]
[2020-01-03 11:48:27:0828] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:29:0221] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:31:0069] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:32:0445] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:33:0557] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:34:0445] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:36:0214] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:48:38:0264] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:48:38:0298] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:49:37:0517] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:49:38:0856] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:49:38:0900] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1152]
[2020-01-03 11:49:38:0900] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8760]
[2020-01-03 11:49:40:0960] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:49:46:0924] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:49:47:0695] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:49:47:0701] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:54:01:0989] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:54:03:0605] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:54:03:0650] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2808]
[2020-01-03 11:54:03:0650] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1984]
[2020-01-03 11:54:09:0399] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:54:10:0735] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:54:10:0751] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:54:26:0261] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:54:27:0587] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:54:27:0634] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9304]
[2020-01-03 11:54:27:0635] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8188]
[2020-01-03 11:54:31:0691] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:54:32:0351] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:54:32:0435] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:54:45:0471] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:54:47:0221] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:54:47:0265] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9928]
[2020-01-03 11:54:47:0265] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8760]
[2020-01-03 11:54:51:0029] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:54:53:0084] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:54:56:0169] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:54:56:0731] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:54:56:0766] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 11:55:08:0585] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 11:55:10:0124] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 11:55:10:0169] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8348]
[2020-01-03 11:55:10:0169] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9000]
[2020-01-03 11:55:11:0708] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:55:17:0028] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 11:55:18:0991] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 11:55:19:0070] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 12:00:08:0480] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 12:00:10:0155] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 12:00:10:0200] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9300]
[2020-01-03 12:00:10:0200] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7344]
[2020-01-03 12:00:16:0272] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 12:00:17:0201] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 12:00:17:0201] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 13:45:10:0397] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:45:14:0102] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 13:45:14:0150] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5812]
[2020-01-03 13:45:14:0151] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9704]
[2020-01-03 13:45:20:0113] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 13:45:22:0285] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 13:45:22:0352] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 13:47:48:0743] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:48:24:0107] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:48:29:0398] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 13:48:29:0447] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6312]
[2020-01-03 13:48:29:0447] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8316]
[2020-01-03 13:48:31:0138] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 13:48:32:0162] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 13:48:32:0248] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 13:49:10:0994] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:50:01:0283] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:50:13:0172] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:50:22:0696] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:52:53:0651] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:53:02:0561] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:54:37:0554] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:54:43:0150] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 13:54:44:0742] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 13:54:44:0788] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9592]
[2020-01-03 13:54:44:0789] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9792]
[2020-01-03 13:54:59:0270] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 13:55:15:0391] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 13:55:16:0812] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 13:55:16:0891] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:04:15:0623] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:04:19:0104] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:04:19:0149] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2744]
[2020-01-03 14:04:19:0150] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1152]
[2020-01-03 14:04:24:0553] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:04:33:0976] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:04:34:0051] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:05:56:0434] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:05:58:0287] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:05:58:0333] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1028]
[2020-01-03 14:05:58:0334] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1308]
[2020-01-03 14:06:07:0042] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:06:08:0199] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:06:08:0236] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:13:41:0295] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:13:57:0017] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:13:59:0684] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-03 14:14:04:0041] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:14:05:0973] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-03 14:15:29:0711] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:15:31:0985] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:15:32:0031] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2120]
[2020-01-03 14:15:32:0032] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9928]
[2020-01-03 14:15:40:0940] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:15:42:0303] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:15:42:0333] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:16:03:0989] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:16:47:0242] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:16:51:0953] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-03 14:16:56:0542] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:16:58:0994] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-03 14:17:03:0368] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:17:04:0748] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-03 14:17:53:0289] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:18:04:0899] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:18:04:0946] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10176]
[2020-01-03 14:18:04:0947] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8520]
[2020-01-03 14:18:17:0574] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:18:18:0736] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:18:18:0748] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:18:59:0320] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:19:04:0468] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:19:04:0513] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5748]
[2020-01-03 14:19:04:0513] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9076]
[2020-01-03 14:19:09:0820] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:19:10:0661] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:19:10:0714] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:39:32:0295] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:44:32:0850] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:44:35:0593] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:44:35:0603] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:44:35:0650] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5920]
[2020-01-03 14:44:35:0650] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10148]
[2020-01-03 14:45:23:0558] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:45:23:0653] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:45:30:0045] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:45:48:0370] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:45:51:0045] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:45:56:0087] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:45:56:0133] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7192]
[2020-01-03 14:45:56:0133] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7592]
[2020-01-03 14:46:03:0961] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:46:06:0982] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:46:09:0727] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:46:11:0599] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:46:11:0633] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 14:47:07:0712] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 14:47:12:0928] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:47:12:0942] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 14:47:12:0989] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9956]
[2020-01-03 14:47:12:0989] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8696]
[2020-01-03 14:47:50:0459] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 14:47:51:0099] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 14:47:51:0191] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 15:17:17:0255] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 15:20:33:0370] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 15:20:36:0425] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:20:36:0435] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 15:20:36:0475] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6604]
[2020-01-03 15:20:36:0475] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7892]
[2020-01-03 15:20:39:0009] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:20:43:0536] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:20:57:0682] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:21:00:0066] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:21:03:0856] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:21:04:0441] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 15:21:04:0477] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 15:22:30:0787] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 15:22:33:0978] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:22:33:0990] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 15:22:34:0030] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8000]
[2020-01-03 15:22:34:0031] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2328]
[2020-01-03 15:22:47:0222] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:22:48:0291] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 15:22:48:0334] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 15:23:27:0999] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 15:23:45:0228] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 15:23:47:0786] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:23:47:0796] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 15:23:47:0840] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7636]
[2020-01-03 15:23:47:0840] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7608]
[2020-01-03 15:23:51:0426] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 15:23:52:0266] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 15:23:52:0340] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 17:48:04:0391] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:48:33:0744] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:49:06:0885] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:49:41:0561] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:50:15:0626] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:50:32:0649] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:51:35:0490] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:51:38:0541] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 17:51:38:0552] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 17:51:38:0597] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1524]
[2020-01-03 17:51:38:0598] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9680]
[2020-01-03 17:51:55:0205] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 17:51:59:0581] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 17:51:59:0599] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 17:53:31:0220] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:54:09:0158] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:54:29:0020] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:55:00:0710] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:57:28:0453] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:57:55:0929] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 17:57:59:0227] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 17:57:59:0242] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-03 17:57:59:0288] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5588]
[2020-01-03 17:57:59:0288] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6244]
[2020-01-03 17:58:21:0903] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-03 17:58:24:0772] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-03 17:58:24:0789] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-03 18:00:21:0920] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 18:00:32:0181] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-03 18:00:38:0192] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:24:54:0087] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:25:03:0474] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:25:03:0488] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-06 16:25:03:0535] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3128]
[2020-01-06 16:25:03:0535] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2644]
[2020-01-06 16:25:16:0009] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:25:16:0036] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-06 16:25:16:0099] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-06 16:25:44:0251] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:25:47:0079] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:25:47:0091] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-06 16:25:47:0134] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[368]
[2020-01-06 16:25:47:0134] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4460]
[2020-01-06 16:27:11:0457] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:27:13:0665] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-06 16:27:16:0596] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:27:18:0946] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-06 16:28:26:0259] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:28:29:0314] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:28:29:0327] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-06 16:28:29:0368] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6592]
[2020-01-06 16:28:29:0368] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4384]
[2020-01-06 16:28:51:0055] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:28:52:0620] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-06 16:28:52:0670] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-06 16:30:17:0581] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:30:19:0965] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:30:19:0978] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-06 16:30:20:0019] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8060]
[2020-01-06 16:30:20:0019] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2780]
[2020-01-06 16:30:52:0684] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:30:53:0566] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-06 16:30:53:0620] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-06 16:31:50:0737] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:31:53:0460] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:31:53:0472] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-06 16:31:53:0513] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6884]
[2020-01-06 16:31:53:0513] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6424]
[2020-01-06 16:32:24:0628] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:32:25:0444] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-06 16:32:25:0515] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-06 16:38:50:0875] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-06 16:38:53:0443] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:38:53:0456] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-06 16:38:53:0503] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9764]
[2020-01-06 16:38:53:0503] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3636]
[2020-01-06 16:39:17:0697] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-06 16:39:18:0260] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-06 16:39:18:0304] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-07 19:25:15:0528] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 08:35:28:0956] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:19:39:0999] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:20:58:0852] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:22:47:0808] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:22:51:0898] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 10:22:51:0908] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 10:22:51:0959] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9112]
[2020-01-08 10:22:51:0960] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7304]
[2020-01-08 10:23:02:0678] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 10:23:12:0347] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-08 10:23:12:0361] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-08 10:24:52:0670] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:42:48:0810] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:43:17:0527] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:44:12:0638] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:45:00:0839] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:49:14:0158] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:49:50:0817] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 10:52:22:0464] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 11:13:36:0796] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 13:54:37:0213] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 14:35:20:0058] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 14:36:22:0128] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 14:46:27:0846] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 15:40:56:0552] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 16:09:50:0681] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:14:43:0772] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:15:18:0472] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:15:18:0482] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:15:18:0534] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8780]
[2020-01-08 17:15:18:0534] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10004]
[2020-01-08 17:15:22:0088] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:15:23:0772] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-08 17:15:23:0835] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-08 17:24:10:0703] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:26:12:0988] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:26:13:0488] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:26:17:0164] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:26:17:0175] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:26:17:0222] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11088]
[2020-01-08 17:26:17:0222] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2964]
[2020-01-08 17:26:18:0817] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:26:18:0826] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:26:18:0871] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8060]
[2020-01-08 17:26:18:0871] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10708]
[2020-01-08 17:26:30:0309] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:26:50:0319] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:26:51:0219] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:27:01:0593] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:27:24:0903] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:27:29:0250] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:27:30:0570] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:27:33:0985] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:27:35:0305] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:28:51:0527] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:28:52:0000] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:28:54:0267] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:28:54:0278] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:28:54:0321] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10884]
[2020-01-08 17:28:54:0321] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10512]
[2020-01-08 17:29:01:0131] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:29:02:0611] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:29:02:0621] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:29:02:0667] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10944]
[2020-01-08 17:29:02:0668] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8864]
[2020-01-08 17:29:43:0813] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:29:45:0370] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:29:47:0303] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:31:08:0071] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:31:08:0544] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:31:10:0226] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:31:10:0236] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:31:10:0283] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7296]
[2020-01-08 17:31:10:0283] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6664]
[2020-01-08 17:31:11:0599] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:31:11:0608] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:31:11:0649] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8664]
[2020-01-08 17:31:11:0650] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6100]
[2020-01-08 17:31:23:0595] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:31:30:0122] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:31:32:0800] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-08 17:31:32:0886] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-08 17:31:33:0502] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-08 17:31:33:0552] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-08 17:54:59:0056] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:54:59:0526] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-08 17:55:02:0280] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:55:02:0290] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:55:02:0335] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8324]
[2020-01-08 17:55:02:0336] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9012]
[2020-01-08 17:55:08:0081] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:55:08:0090] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-08 17:55:08:0132] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8372]
[2020-01-08 17:55:08:0133] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5216]
[2020-01-08 17:55:15:0077] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:55:22:0183] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:55:23:0207] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:55:33:0704] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-08 17:56:05:0539] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-08 17:56:05:0544] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-08 17:56:05:0803] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-08 17:56:05:0836] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:24:15:0786] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:24:16:0265] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:24:20:0376] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:24:20:0389] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:24:20:0433] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8164]
[2020-01-09 10:24:20:0433] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8648]
[2020-01-09 10:24:21:0276] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:24:21:0285] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:24:21:0327] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7316]
[2020-01-09 10:24:21:0327] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9532]
[2020-01-09 10:24:29:0006] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:24:30:0261] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:24:57:0095] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:25:07:0553] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:25:18:0931] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:25:18:0936] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:25:19:0021] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 10:25:20:0033] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:25:20:0083] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 10:26:12:0454] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:26:12:0929] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:26:17:0687] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:26:17:0701] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:26:17:0747] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6916]
[2020-01-09 10:26:17:0747] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8652]
[2020-01-09 10:26:47:0807] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:26:49:0735] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-09 10:26:54:0421] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:26:56:0886] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-09 10:27:00:0546] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:27:03:0247] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-09 10:29:39:0771] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:29:40:0243] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:29:43:0677] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:29:43:0692] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:29:43:0738] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2880]
[2020-01-09 10:29:43:0739] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9968]
[2020-01-09 10:30:03:0527] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:30:03:0541] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:30:03:0583] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5872]
[2020-01-09 10:30:03:0583] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2460]
[2020-01-09 10:32:31:0795] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:32:32:0273] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:32:37:0628] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:32:37:0642] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:32:37:0687] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8340]
[2020-01-09 10:32:37:0687] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9220]
[2020-01-09 10:34:01:0251] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 10:34:01:0292] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:35:46:0331] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:35:46:0802] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:35:49:0826] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:35:49:0839] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:35:49:0882] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6524]
[2020-01-09 10:35:49:0882] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5404]
[2020-01-09 10:36:50:0547] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 10:36:50:0585] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:38:25:0400] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:38:25:0871] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 10:38:29:0721] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:38:29:0735] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:38:29:0780] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9420]
[2020-01-09 10:38:29:0780] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4004]
[2020-01-09 10:39:31:0207] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:40:19:0506] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:42:31:0794] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:42:31:0809] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 10:42:31:0855] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7700]
[2020-01-09 10:42:31:0856] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6896]
[2020-01-09 10:45:41:0980] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 10:45:42:0067] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:45:42:0070] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 10:45:43:0205] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 10:45:43:0221] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 15:43:47:0177] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:43:47:0655] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:43:58:0930] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:43:58:0940] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 15:43:58:0985] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[868]
[2020-01-09 15:43:58:0985] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6548]
[2020-01-09 15:44:59:0871] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:45:13:0669] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-09 15:46:07:0298] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:46:07:0768] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:46:53:0012] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:46:53:0482] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:47:25:0155] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:47:25:0628] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:47:29:0266] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:47:29:0278] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 15:47:29:0323] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[13264]
[2020-01-09 15:47:29:0323] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[13268]
[2020-01-09 15:47:30:0623] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:47:30:0634] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 15:47:30:0681] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[13284]
[2020-01-09 15:47:30:0682] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[13288]
[2020-01-09 15:47:38:0112] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:47:39:0207] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:48:00:0672] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:48:07:0489] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:48:09:0193] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 15:48:09:0225] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 15:48:10:0187] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 15:48:10:0191] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 15:48:14:0419] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:48:14:0893] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 15:48:17:0554] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:48:17:0564] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-09 15:48:17:0611] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[13252]
[2020-01-09 15:48:17:0611] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[13256]
[2020-01-09 15:48:21:0416] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-09 15:48:22:0909] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-09 15:48:22:0912] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-09 17:20:49:0193] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 17:20:49:0685] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 17:22:33:0715] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 17:22:34:0205] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 17:57:29:0546] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 17:57:30:0068] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 18:01:21:0864] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 18:01:22:0335] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 18:02:16:0951] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 18:02:17:0432] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 18:03:00:0929] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-09 18:03:01:0405] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:49:08:0165] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:49:08:0650] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:49:35:0469] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:49:35:0937] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:50:14:0357] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:50:14:0825] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:50:44:0694] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:50:45:0164] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:51:07:0452] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:51:07:0920] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:56:34:0010] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:56:34:0480] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 09:56:36:0697] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 09:56:36:0707] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 09:56:36:0751] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6152]
[2020-01-15 09:56:36:0751] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6148]
[2020-01-15 09:56:37:0582] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 09:56:37:0591] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 09:56:37:0632] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1560]
[2020-01-15 09:56:37:0632] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5332]
[2020-01-15 10:06:23:0606] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:06:24:0075] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:06:25:0882] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:06:25:0891] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:06:25:0931] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[344]
[2020-01-15 10:06:25:0931] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7108]
[2020-01-15 10:06:27:0043] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:06:27:0053] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:06:27:0097] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4304]
[2020-01-15 10:06:27:0098] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3048]
[2020-01-15 10:07:53:0331] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:07:54:0539] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:08:05:0787] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:08:05:0854] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:08:06:0354] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:08:06:0385] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:08:23:0788] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:08:24:0257] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:11:27:0892] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:11:28:0362] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:18:45:0937] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:18:46:0405] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:21:40:0135] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:21:40:0607] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:24:21:0640] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:24:22:0109] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:24:43:0878] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:24:57:0618] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:00:0829] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:03:0787] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:07:0139] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:08:0827] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:11:0888] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:13:0972] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:17:0167] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:19:0414] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:22:0430] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:24:0238] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:27:0630] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:29:0559] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 10:25:32:0666] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:25:33:0135] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:29:13:0083] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:29:13:0553] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:32:07:0439] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:32:07:0909] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:32:48:0320] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:32:48:0334] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:32:48:0378] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7304]
[2020-01-15 10:32:48:0379] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3504]
[2020-01-15 10:36:42:0083] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:36:42:0098] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:36:42:0145] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4580]
[2020-01-15 10:36:42:0145] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7064]
[2020-01-15 10:39:10:0838] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:39:10:0901] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:39:10:0928] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:39:12:0153] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:39:12:0166] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:39:17:0186] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:39:17:0665] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:41:19:0119] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:41:19:0603] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:42:09:0865] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:42:10:0364] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:42:31:0343] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:42:31:0834] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:42:54:0751] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:42:55:0232] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:43:00:0171] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:43:00:0186] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:43:00:0232] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5836]
[2020-01-15 10:43:00:0232] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4736]
[2020-01-15 10:46:00:0843] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:46:00:0933] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:46:00:0942] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:46:36:0164] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:46:36:0646] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:46:39:0805] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:46:39:0820] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:46:39:0866] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6836]
[2020-01-15 10:46:39:0866] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7864]
[2020-01-15 10:47:03:0956] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:47:03:0970] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:47:04:0016] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7748]
[2020-01-15 10:47:04:0016] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6824]
[2020-01-15 10:49:11:0364] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:49:11:0374] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:49:11:0454] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:49:11:0823] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:49:11:0871] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:50:06:0706] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:50:07:0180] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:50:12:0610] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:50:12:0624] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:50:12:0669] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6076]
[2020-01-15 10:50:12:0670] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1864]
[2020-01-15 10:50:45:0900] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:50:45:0972] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:50:45:0990] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:50:50:0486] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:50:50:0957] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:50:54:0088] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:50:54:0102] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:50:54:0148] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4568]
[2020-01-15 10:50:54:0148] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3276]
[2020-01-15 10:52:02:0337] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:52:02:0351] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:52:02:0398] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5260]
[2020-01-15 10:52:02:0398] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7300]
[2020-01-15 10:52:17:0643] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:52:17:0653] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:52:17:0733] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:52:18:0839] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 10:52:18:0899] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 10:52:55:0148] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:52:55:0619] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:59:08:0304] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:59:08:0774] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 10:59:13:0668] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:59:13:0682] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:59:13:0727] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5772]
[2020-01-15 10:59:13:0728] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1708]
[2020-01-15 10:59:25:0129] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 10:59:25:0143] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 10:59:25:0189] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1796]
[2020-01-15 10:59:25:0189] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4428]
[2020-01-15 11:04:47:0553] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:04:47:0643] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:04:47:0650] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:04:48:0874] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:04:48:0908] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:05:33:0552] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:05:34:0026] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:05:38:0974] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:05:38:0988] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:05:39:0033] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8048]
[2020-01-15 11:05:39:0034] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5792]
[2020-01-15 11:06:02:0054] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:06:02:0135] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:06:02:0144] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:06:06:0105] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:06:06:0580] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:06:09:0355] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:06:09:0369] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:06:09:0414] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7608]
[2020-01-15 11:06:09:0414] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7116]
[2020-01-15 11:08:21:0526] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:08:21:0616] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:08:21:0621] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:10:19:0424] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:10:19:0943] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:10:32:0760] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:10:32:0774] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:10:32:0818] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7460]
[2020-01-15 11:10:32:0819] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2804]
[2020-01-15 11:11:26:0302] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:11:26:0322] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:11:26:0392] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:11:32:0530] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:11:33:0005] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:11:36:0003] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:11:36:0017] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:11:36:0062] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5388]
[2020-01-15 11:11:36:0062] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5140]
[2020-01-15 11:12:10:0853] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:12:10:0864] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:12:10:0943] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:13:59:0087] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:13:59:0565] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:14:01:0915] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:14:01:0929] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:14:01:0973] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7908]
[2020-01-15 11:14:01:0973] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7708]
[2020-01-15 11:14:14:0985] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:14:15:0074] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:14:15:0075] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:21:00:0469] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:21:00:0945] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:21:35:0414] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:21:35:0890] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:22:16:0340] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:22:16:0812] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:23:21:0224] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:23:21:0696] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:23:59:0558] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:24:00:0037] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:24:03:0185] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:24:03:0200] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:24:03:0246] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6280]
[2020-01-15 11:24:03:0247] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6808]
[2020-01-15 11:24:15:0238] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:24:15:0252] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:24:15:0297] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7932]
[2020-01-15 11:24:15:0298] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5100]
[2020-01-15 11:24:25:0584] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:24:25:0648] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:24:25:0674] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:24:26:0699] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:24:26:0743] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:24:31:0058] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:24:31:0530] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:24:44:0387] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:24:44:0862] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:24:48:0657] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:24:48:0672] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:24:48:0718] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6836]
[2020-01-15 11:24:48:0718] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7172]
[2020-01-15 11:26:43:0526] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:26:47:0238] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 11:26:50:0246] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:26:52:0126] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 11:26:55:0626] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:26:57:0111] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 11:27:00:0599] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:27:02:0008] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 11:27:05:0665] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:27:08:0265] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 11:27:35:0119] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:27:36:0821] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-01-15 11:27:39:0325] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:27:39:0796] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:27:43:0525] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:27:43:0539] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:27:43:0585] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7396]
[2020-01-15 11:27:43:0585] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6492]
[2020-01-15 11:30:28:0670] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:30:28:0695] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:30:28:0760] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-01-15 11:30:42:0365] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:30:42:0839] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-01-15 11:30:47:0043] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:30:47:0058] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-01-15 11:30:47:0103] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6776]
[2020-01-15 11:30:47:0103] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6928]
[2020-01-15 11:35:02:0109] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-01-15 11:35:02:0118] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-01-15 11:35:02:0199] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-03 15:15:59:0688] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:16:00:0354] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:16:14:0538] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:16:14:0548] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:16:14:0577] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4316]
[2020-06-03 15:16:14:0577] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4320]
[2020-06-03 15:16:16:0886] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:16:16:0896] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:16:16:0925] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4352]
[2020-06-03 15:16:16:0925] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4356]
[2020-06-03 15:27:35:0360] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:27:40:0268] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:27:41:0208] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:29:38:0567] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:29:40:0184] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:29:44:0538] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:30:39:0922] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:30:43:0045] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-06-03 15:30:46:0509] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:30:48:0100] | CInterfaceManager.cpp-L0870: close device fail![80000003]
[2020-06-03 15:31:05:0651] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:31:06:0233] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:31:15:0538] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:31:15:0548] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:31:15:0576] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4896]
[2020-06-03 15:31:15:0576] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4860]
[2020-06-03 15:32:23:0700] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:36:27:0862] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:36:28:0364] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:36:40:0537] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:36:40:0546] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:36:40:0575] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5076]
[2020-06-03 15:36:40:0575] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1448]
[2020-06-03 15:37:08:0911] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:37:09:0413] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:37:14:0808] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:37:15:0363] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:37:38:0172] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:37:38:0714] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:38:00:0525] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:38:01:0035] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:38:08:0053] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:08:0062] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:38:08:0091] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4316]
[2020-06-03 15:38:08:0091] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[888]
[2020-06-03 15:38:10:0390] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:11:0436] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:11:0445] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:38:11:0475] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5072]
[2020-06-03 15:38:11:0476] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4668]
[2020-06-03 15:38:12:0788] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:17:0796] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:19:0402] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:25:0587] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:31:0255] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:38:31:0318] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-03 15:38:31:0392] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-03 15:38:37:0451] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:38:38:0005] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:40:02:0694] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:40:03:0196] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:40:10:0515] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:40:10:0528] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:40:10:0560] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4320]
[2020-06-03 15:40:10:0560] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5096]
[2020-06-03 15:40:15:0642] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:40:15:0655] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-03 15:40:15:0687] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3504]
[2020-06-03 15:40:15:0687] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4648]
[2020-06-03 15:43:07:0831] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:43:08:0730] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:43:17:0151] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:43:19:0826] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-03 15:46:37:0583] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:46:43:0390] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-03 15:47:03:0171] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:50:39:0776] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:50:40:0272] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:50:44:0146] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:50:44:0157] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:50:44:0191] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3752]
[2020-06-05 13:50:44:0191] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3224]
[2020-06-05 13:50:45:0461] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:50:45:0473] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:50:45:0510] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4708]
[2020-06-05 13:50:45:0510] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4100]
[2020-06-05 13:50:47:0797] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:50:49:0020] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:56:43:0692] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:56:48:0348] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:57:18:0679] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:57:19:0229] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:57:21:0725] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:57:21:0739] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:57:21:0774] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[872]
[2020-06-05 13:57:21:0774] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4836]
[2020-06-05 13:57:23:0164] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:57:23:0178] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:57:23:0213] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2788]
[2020-06-05 13:57:23:0214] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3136]
[2020-06-05 13:58:48:0978] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:58:49:0533] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:58:53:0106] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:58:53:0120] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:58:53:0155] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[788]
[2020-06-05 13:58:53:0155] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[304]
[2020-06-05 13:58:55:0028] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:58:55:0042] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:58:55:0078] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3692]
[2020-06-05 13:58:55:0079] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3196]
[2020-06-05 13:59:08:0301] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:59:08:0356] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 13:59:08:0391] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 13:59:09:0280] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 13:59:09:0308] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 13:59:12:0491] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:59:13:0046] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:59:15:0891] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:59:15:0905] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 13:59:15:0941] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3132]
[2020-06-05 13:59:15:0941] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3188]
[2020-06-05 13:59:18:0524] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 13:59:58:0863] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 13:59:59:0410] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:00:06:0250] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:00:06:0264] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:00:06:0301] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4940]
[2020-06-05 14:00:06:0301] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2800]
[2020-06-05 14:00:35:0130] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:00:35:0673] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:00:38:0306] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:00:38:0322] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:00:38:0360] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3224]
[2020-06-05 14:00:38:0360] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4936]
[2020-06-05 14:01:55:0288] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:01:55:0841] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:01:58:0961] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:01:58:0974] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:01:59:0011] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4980]
[2020-06-05 14:01:59:0011] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4016]
[2020-06-05 14:06:33:0726] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:07:03:0156] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:07:03:0698] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:07:12:0376] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:07:12:0889] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:07:21:0311] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:07:35:0488] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:07:35:0523] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2164]
[2020-06-05 14:07:35:0523] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5116]
[2020-06-05 14:07:57:0582] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:07:57:0596] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:07:57:0631] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2184]
[2020-06-05 14:07:57:0631] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3976]
[2020-06-05 14:08:28:0017] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:08:28:0027] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:08:33:0346] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:08:33:0871] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:08:57:0900] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:08:58:0410] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:09:37:0041] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:09:37:0555] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:09:47:0911] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:09:48:0415] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:09:52:0006] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:09:52:0020] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:09:52:0055] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4520]
[2020-06-05 14:09:52:0055] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2412]
[2020-06-05 14:09:55:0725] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:09:55:0739] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:09:55:0773] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2308]
[2020-06-05 14:09:55:0773] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2784]
[2020-06-05 14:09:58:0681] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:09:58:0756] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:09:58:0771] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:09:59:0573] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:09:59:0627] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:10:14:0377] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:10:14:0928] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:10:34:0556] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:10:35:0087] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:11:05:0307] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:11:05:0828] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:13:01:0214] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:13:01:0228] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:13:01:0266] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3864]
[2020-06-05 14:13:01:0266] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3320]
[2020-06-05 14:13:04:0783] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:13:08:0764] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:13:09:0299] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:14:18:0442] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:14:18:0951] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:14:21:0996] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:14:26:0291] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:14:26:0327] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4472]
[2020-06-05 14:14:26:0327] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4812]
[2020-06-05 14:14:31:0115] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:14:31:0129] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:14:31:0164] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3744]
[2020-06-05 14:14:31:0164] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1344]
[2020-06-05 14:14:52:0206] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:14:52:0228] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:14:52:0296] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:14:53:0063] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:14:53:0065] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:15:26:0643] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:15:27:0177] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:15:30:0230] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:15:30:0244] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:15:30:0280] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4408]
[2020-06-05 14:15:30:0280] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2516]
[2020-06-05 14:15:53:0527] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:15:53:0541] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:15:53:0577] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4744]
[2020-06-05 14:15:53:0577] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2800]
[2020-06-05 14:16:36:0926] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:16:36:0979] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:16:37:0016] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:16:57:0151] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:16:57:0684] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:19:32:0017] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:19:32:0545] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:19:44:0684] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:19:44:0699] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:19:44:0735] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2184]
[2020-06-05 14:19:44:0735] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[300]
[2020-06-05 14:20:15:0278] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:20:21:0609] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:20:22:0156] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:21:30:0513] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:21:30:0527] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:21:30:0563] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2980]
[2020-06-05 14:21:30:0564] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3020]
[2020-06-05 14:22:58:0855] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:22:59:0405] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:23:05:0052] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:23:05:0066] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:23:05:0101] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3100]
[2020-06-05 14:23:05:0102] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[596]
[2020-06-05 14:23:33:0769] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:23:43:0385] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:23:43:0920] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:23:48:0372] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:23:48:0386] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:23:48:0422] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4724]
[2020-06-05 14:23:48:0422] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5048]
[2020-06-05 14:24:09:0058] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:24:13:0859] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:24:14:0406] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:24:15:0110] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:24:15:0124] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:24:15:0162] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5036]
[2020-06-05 14:24:15:0162] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4284]
[2020-06-05 14:24:36:0186] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:24:57:0681] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:24:58:0217] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:24:58:0936] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:24:58:0951] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:24:58:0987] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1392]
[2020-06-05 14:24:58:0988] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4232]
[2020-06-05 14:25:10:0162] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:25:35:0476] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:25:36:0021] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:25:36:0729] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:25:36:0743] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:25:36:0779] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3048]
[2020-06-05 14:25:36:0779] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4676]
[2020-06-05 14:25:57:0290] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:27:08:0763] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:27:09:0304] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:27:10:0009] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:27:10:0025] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:27:10:0062] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3136]
[2020-06-05 14:27:10:0062] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3544]
[2020-06-05 14:27:46:0466] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:27:47:0012] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:27:47:0703] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:27:47:0720] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:27:47:0758] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3396]
[2020-06-05 14:27:47:0758] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4716]
[2020-06-05 14:29:09:0939] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:29:10:0496] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:29:11:0184] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:29:11:0198] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:29:11:0235] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4340]
[2020-06-05 14:29:11:0235] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3104]
[2020-06-05 14:29:24:0560] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:29:53:0050] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:29:53:0588] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:30:02:0479] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:30:02:0989] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:30:06:0585] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:30:06:0599] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:30:06:0634] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4052]
[2020-06-05 14:30:06:0634] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4296]
[2020-06-05 14:30:08:0728] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:30:13:0240] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:30:13:0784] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:30:18:0159] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:30:18:0173] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:30:18:0209] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5036]
[2020-06-05 14:30:18:0209] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2504]
[2020-06-05 14:30:21:0382] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:30:21:0395] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:30:21:0432] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3448]
[2020-06-05 14:30:21:0432] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1516]
[2020-06-05 14:30:23:0760] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:30:23:0832] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:30:23:0850] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:30:25:0044] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:30:25:0109] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:32:14:0779] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:32:15:0305] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:32:18:0448] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:32:18:0462] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:32:18:0497] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4792]
[2020-06-05 14:32:18:0497] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4232]
[2020-06-05 14:32:20:0557] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:32:20:0571] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:32:20:0608] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5004]
[2020-06-05 14:32:20:0608] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3784]
[2020-06-05 14:32:21:0967] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:32:21:0997] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:32:22:0057] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:32:22:0974] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:32:23:0008] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:33:09:0506] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:33:10:0038] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:33:12:0422] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:33:12:0437] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:33:12:0472] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3492]
[2020-06-05 14:33:12:0473] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3328]
[2020-06-05 14:33:15:0621] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:33:15:0636] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:33:15:0672] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4180]
[2020-06-05 14:33:15:0673] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4420]
[2020-06-05 14:33:17:0430] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:33:17:0473] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:33:17:0520] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:33:18:0970] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:33:18:0973] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:34:44:0535] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:34:45:0064] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:34:47:0692] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:34:47:0707] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:34:47:0744] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4924]
[2020-06-05 14:34:47:0745] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3972]
[2020-06-05 14:34:49:0612] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:34:49:0626] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:34:49:0662] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3132]
[2020-06-05 14:34:49:0663] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3676]
[2020-06-05 14:35:01:0510] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:35:01:0546] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:35:01:0600] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:35:02:0363] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:35:02:0421] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:35:05:0471] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:35:06:0030] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:35:12:0534] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:35:12:0549] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:35:12:0585] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4292]
[2020-06-05 14:35:12:0585] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4408]
[2020-06-05 14:35:15:0669] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:46:31:0433] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:46:31:0974] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:46:35:0634] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:46:35:0648] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:46:35:0683] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5088]
[2020-06-05 14:46:35:0684] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3492]
[2020-06-05 14:46:37:0133] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:46:37:0147] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:46:37:0183] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4504]
[2020-06-05 14:46:37:0183] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3708]
[2020-06-05 14:47:22:0317] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:47:22:0853] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:47:27:0232] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:47:27:0246] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:47:27:0281] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5012]
[2020-06-05 14:47:27:0281] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3992]
[2020-06-05 14:48:36:0826] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:48:37:0363] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:48:41:0560] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:48:41:0574] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:48:41:0609] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4684]
[2020-06-05 14:48:41:0609] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4188]
[2020-06-05 14:49:43:0718] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:49:44:0265] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:49:54:0759] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:49:54:0773] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:49:54:0808] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3368]
[2020-06-05 14:49:54:0808] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4896]
[2020-06-05 14:50:10:0630] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:50:10:0644] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:50:10:0680] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4940]
[2020-06-05 14:50:10:0681] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2344]
[2020-06-05 14:51:35:0686] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:51:35:0776] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:51:35:0786] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:51:36:0366] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 14:51:36:0414] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:51:39:0748] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:51:40:0301] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 14:52:12:0297] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 14:52:12:0311] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 14:52:12:0346] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4244]
[2020-06-05 14:52:12:0346] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4832]
[2020-06-05 14:52:20:0347] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 14:52:20:0443] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 15:03:15:0781] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:03:16:0285] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:03:18:0530] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:03:18:0544] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 15:03:18:0579] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5096]
[2020-06-05 15:03:18:0580] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4904]
[2020-06-05 15:03:30:0498] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:03:30:0512] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 15:03:30:0546] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4688]
[2020-06-05 15:03:30:0546] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2196]
[2020-06-05 15:07:19:0304] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:07:19:0394] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 15:07:19:0394] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 15:07:20:0448] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 15:07:20:0459] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 15:07:26:0880] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:07:27:0420] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:07:54:0702] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:07:55:0203] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:08:06:0192] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:08:06:0206] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 15:08:06:0241] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2092]
[2020-06-05 15:08:06:0241] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5888]
[2020-06-05 15:08:47:0643] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:08:47:0658] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 15:08:47:0694] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5728]
[2020-06-05 15:08:47:0695] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3864]
[2020-06-05 15:09:12:0855] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:09:12:0897] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 15:09:12:0945] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 15:09:13:0545] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-05 15:09:13:0616] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 15:10:24:0856] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:10:25:0383] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-05 15:10:37:0029] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-05 15:10:37:0042] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-05 15:10:37:0075] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4404]
[2020-06-05 15:10:37:0076] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2704]
[2020-06-05 15:14:48:0570] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-05 15:14:48:0591] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 09:57:56:0690] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 09:57:57:0196] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 09:59:58:0795] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 09:59:58:0809] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 09:59:58:0844] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5096]
[2020-06-06 09:59:58:0844] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5100]
[2020-06-06 10:00:47:0005] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 10:00:47:0018] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 10:00:47:0054] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4420]
[2020-06-06 10:00:47:0054] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4404]
[2020-06-06 10:09:07:0078] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 10:09:07:0168] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 10:09:07:0176] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 10:09:07:0383] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 10:09:07:0388] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 10:09:34:0158] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:09:34:0694] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:09:54:0851] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:09:55:0366] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:21:45:0162] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:21:45:0683] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:22:25:0196] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:22:25:0701] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:22:42:0090] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:22:42:0601] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:24:21:0051] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:24:21:0576] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:26:06:0066] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:26:06:0601] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:28:48:0530] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 10:28:48:0544] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 10:28:48:0581] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4328]
[2020-06-06 10:28:48:0581] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3900]
[2020-06-06 10:30:24:0263] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:30:32:0959] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:30:33:0507] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:30:36:0742] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 10:30:36:0756] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 10:30:36:0791] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2336]
[2020-06-06 10:30:36:0791] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1524]
[2020-06-06 10:33:26:0044] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:33:26:0598] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:40:26:0593] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:40:27:0131] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:47:48:0082] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:47:48:0604] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:48:21:0538] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:48:22:0061] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:49:20:0081] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:49:20:0594] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:49:38:0993] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 10:49:39:0518] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 11:26:39:0992] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 11:26:40:0489] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:39:25:0566] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:39:26:0079] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:40:55:0162] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:40:55:0680] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:41:41:0780] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:41:42:0314] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:42:43:0101] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:42:43:0607] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:44:17:0146] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:44:17:0664] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:47:40:0707] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:47:41:0238] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:48:42:0159] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:48:42:0682] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:49:16:0314] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:49:16:0840] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:50:13:0585] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:50:14:0104] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:50:53:0578] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 13:50:53:0591] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 13:50:53:0624] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4912]
[2020-06-06 13:50:53:0624] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4276]
[2020-06-06 13:50:56:0039] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 13:50:56:0053] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 13:50:56:0086] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[436]
[2020-06-06 13:50:56:0086] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5124]
[2020-06-06 13:57:11:0487] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 13:57:11:0498] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 13:57:11:0507] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 13:57:12:0238] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 13:57:12:0245] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 13:58:52:0847] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:58:53:0386] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 13:59:18:0323] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 13:59:18:0337] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 13:59:18:0372] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4484]
[2020-06-06 13:59:18:0372] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[644]
[2020-06-06 14:00:17:0418] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:00:17:0432] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:00:17:0468] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1976]
[2020-06-06 14:00:17:0469] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4912]
[2020-06-06 14:02:22:0525] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:02:22:0583] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:02:22:0615] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:02:23:0860] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:02:23:0876] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:06:07:0604] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:06:08:0155] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:06:22:0714] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:06:22:0728] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:06:22:0764] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5316]
[2020-06-06 14:06:22:0765] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1092]
[2020-06-06 14:06:38:0994] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:06:39:0008] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:06:39:0043] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3156]
[2020-06-06 14:06:39:0043] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5664]
[2020-06-06 14:08:34:0698] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:08:34:0772] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:08:34:0788] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:08:34:0949] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:08:35:0017] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:08:43:0638] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:08:44:0174] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:08:59:0191] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:08:59:0710] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:09:04:0269] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:09:04:0283] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:09:04:0319] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3772]
[2020-06-06 14:09:04:0319] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2124]
[2020-06-06 14:09:06:0520] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:09:06:0534] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:09:06:0570] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[976]
[2020-06-06 14:09:06:0570] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6032]
[2020-06-06 14:11:16:0946] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:11:21:0720] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:11:22:0264] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:11:25:0216] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:11:25:0230] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:11:25:0265] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6116]
[2020-06-06 14:11:25:0265] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4736]
[2020-06-06 14:12:57:0633] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:12:57:0647] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:12:57:0683] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3316]
[2020-06-06 14:12:57:0683] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4208]
[2020-06-06 14:14:30:0884] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:14:31:0422] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:14:33:0794] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:14:33:0808] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:14:33:0844] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5164]
[2020-06-06 14:14:33:0844] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4324]
[2020-06-06 14:14:35:0366] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:14:35:0380] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:14:35:0415] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3168]
[2020-06-06 14:14:35:0415] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5724]
[2020-06-06 14:14:53:0607] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:14:53:0645] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:14:53:0697] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:14:54:0716] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:14:54:0773] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:14:58:0654] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:14:59:0191] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:15:15:0181] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:15:15:0703] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:15:40:0509] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:15:40:0523] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:15:40:0558] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5036]
[2020-06-06 14:15:40:0558] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[320]
[2020-06-06 14:15:42:0462] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:15:42:0475] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:15:42:0510] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3644]
[2020-06-06 14:15:42:0510] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2456]
[2020-06-06 14:19:21:0398] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:19:21:0471] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:19:21:0488] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:19:22:0222] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:19:22:0242] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:20:18:0737] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:20:19:0274] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:20:33:0004] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:20:33:0018] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:20:33:0053] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4268]
[2020-06-06 14:20:33:0053] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4384]
[2020-06-06 14:20:34:0994] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:20:35:0008] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:20:35:0043] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4620]
[2020-06-06 14:20:35:0043] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4540]
[2020-06-06 14:34:25:0807] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:34:25:0897] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:34:25:0901] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:34:27:0127] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:34:27:0196] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:34:32:0723] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:34:33:0260] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:34:53:0388] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:34:53:0400] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:34:53:0431] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3920]
[2020-06-06 14:34:53:0431] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5740]
[2020-06-06 14:41:19:0049] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:41:19:0063] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:41:19:0096] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1044]
[2020-06-06 14:41:19:0097] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[368]
[2020-06-06 14:41:45:0588] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:41:45:0599] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:41:45:0678] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:41:46:0955] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:41:46:0966] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:42:32:0375] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:42:32:0918] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:42:35:0309] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-06 14:42:35:0323] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-06 14:42:35:0358] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4000]
[2020-06-06 14:42:35:0358] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5504]
[2020-06-06 14:42:40:0458] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-06 14:42:40:0513] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-06 14:43:44:0577] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-06 14:43:45:0081] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:46:48:0637] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:46:49:0167] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:46:51:0984] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:46:51:0998] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:46:52:0033] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3520]
[2020-06-08 16:46:52:0034] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3148]
[2020-06-08 16:46:54:0071] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:46:54:0085] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:46:54:0120] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4168]
[2020-06-08 16:46:54:0120] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1796]
[2020-06-08 16:46:56:0865] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:46:56:0920] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:46:56:0955] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:46:58:0035] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:46:58:0042] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:48:45:0519] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:48:46:0052] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:48:48:0178] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:48:48:0192] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:48:48:0229] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2952]
[2020-06-08 16:48:48:0229] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2400]
[2020-06-08 16:48:49:0998] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:48:50:0012] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:48:50:0049] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2688]
[2020-06-08 16:48:50:0049] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4408]
[2020-06-08 16:50:27:0215] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:50:27:0258] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:50:27:0305] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:50:28:0584] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:50:28:0637] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:51:36:0079] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:51:36:0613] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:51:39:0390] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:51:39:0404] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:51:39:0440] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4244]
[2020-06-08 16:51:39:0440] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3080]
[2020-06-08 16:51:41:0285] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:51:41:0299] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:51:41:0335] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4268]
[2020-06-08 16:51:41:0335] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1796]
[2020-06-08 16:51:50:0007] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:51:50:0036] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:51:50:0097] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:51:51:0521] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:51:51:0541] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:54:10:0906] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:54:11:0445] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:54:13:0597] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:54:13:0611] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:54:13:0647] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1136]
[2020-06-08 16:54:13:0647] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3344]
[2020-06-08 16:54:15:0117] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:54:15:0131] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:54:15:0167] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4424]
[2020-06-08 16:54:15:0167] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2312]
[2020-06-08 16:54:19:0158] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:54:19:0247] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:54:19:0248] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:54:20:0351] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 16:54:20:0367] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 16:55:50:0910] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:55:51:0447] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:56:03:0347] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:56:03:0361] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:56:03:0397] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3948]
[2020-06-08 16:56:03:0398] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4448]
[2020-06-08 16:56:09:0970] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:56:09:0984] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:56:10:0019] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2504]
[2020-06-08 16:56:10:0019] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3884]
[2020-06-08 16:58:31:0221] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:58:31:0751] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 16:58:33:0779] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:58:33:0793] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:58:33:0828] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4048]
[2020-06-08 16:58:33:0828] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4932]
[2020-06-08 16:58:41:0171] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 16:58:41:0185] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 16:58:41:0221] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1292]
[2020-06-08 16:58:41:0222] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4888]
[2020-06-08 17:00:54:0043] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 17:00:54:0133] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 17:00:54:0139] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 17:02:56:0335] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 17:02:56:0871] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-08 17:02:58:0729] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 17:02:58:0743] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 17:02:58:0778] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3452]
[2020-06-08 17:02:58:0779] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4296]
[2020-06-08 17:03:00:0174] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 17:03:00:0186] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-08 17:03:00:0218] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3520]
[2020-06-08 17:03:00:0218] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4808]
[2020-06-08 17:05:18:0289] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-08 17:05:18:0379] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 17:05:18:0387] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-08 17:05:18:0995] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-08 17:05:19:0032] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 10:20:11:0594] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 10:20:12:0127] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:16:06:0272] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:16:06:0808] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:16:17:0690] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:16:17:0703] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:16:17:0736] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3596]
[2020-06-09 11:16:17:0736] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4380]
[2020-06-09 11:17:30:0101] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:17:33:0906] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:17:34:0452] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:17:39:0943] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:17:39:0957] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:17:39:0993] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1060]
[2020-06-09 11:17:39:0993] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4016]
[2020-06-09 11:17:57:0532] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:19:21:0073] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:19:21:0611] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:19:27:0219] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:19:27:0233] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:19:27:0268] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4672]
[2020-06-09 11:19:27:0268] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4884]
[2020-06-09 11:19:51:0003] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:20:11:0313] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:20:11:0846] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:20:16:0893] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:20:16:0908] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:20:16:0940] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4776]
[2020-06-09 11:20:16:0941] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4280]
[2020-06-09 11:20:39:0483] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:22:31:0104] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:22:31:0655] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:22:37:0586] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:22:37:0600] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:22:37:0635] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1864]
[2020-06-09 11:22:37:0636] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2428]
[2020-06-09 11:22:46:0098] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:22:46:0112] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:22:46:0148] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4064]
[2020-06-09 11:22:46:0148] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1060]
[2020-06-09 11:23:02:0922] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:23:02:0937] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 11:23:03:0012] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 11:23:03:0349] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 11:23:03:0372] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 11:27:25:0692] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:27:26:0256] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:27:44:0304] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:27:44:0813] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:28:01:0961] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:28:01:0975] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:28:02:0009] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4456]
[2020-06-09 11:28:02:0009] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4384]
[2020-06-09 11:28:46:0336] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:29:44:0461] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:29:44:0990] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:29:51:0004] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:29:51:0018] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:29:51:0053] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4952]
[2020-06-09 11:29:51:0054] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5024]
[2020-06-09 11:30:05:0352] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:30:09:0656] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:30:10:0200] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:30:15:0605] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:30:15:0619] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:30:15:0654] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4448]
[2020-06-09 11:30:15:0655] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2428]
[2020-06-09 11:31:38:0231] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:32:11:0099] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:32:11:0628] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:34:44:0780] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:34:44:0795] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:34:44:0831] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1048]
[2020-06-09 11:34:44:0831] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4172]
[2020-06-09 11:34:57:0978] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:34:57:0992] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:34:58:0029] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4536]
[2020-06-09 11:34:58:0029] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1932]
[2020-06-09 11:35:08:0990] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:35:09:0032] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 11:35:09:0080] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 11:35:19:0278] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:35:19:0820] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:35:26:0427] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:35:26:0441] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:35:26:0476] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4640]
[2020-06-09 11:35:26:0477] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3452]
[2020-06-09 11:35:38:0029] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:35:38:0042] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:35:38:0079] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4908]
[2020-06-09 11:35:38:0079] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3916]
[2020-06-09 11:44:01:0190] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:44:01:0720] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:44:02:0434] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:44:02:0448] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:44:02:0480] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4796]
[2020-06-09 11:44:02:0480] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3944]
[2020-06-09 11:47:46:0813] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:47:47:0342] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:47:53:0565] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:47:53:0579] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:47:53:0614] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4464]
[2020-06-09 11:47:53:0614] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4908]
[2020-06-09 11:50:11:0749] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:50:12:0276] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:50:19:0361] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:50:19:0377] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:50:19:0413] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4308]
[2020-06-09 11:50:19:0413] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4796]
[2020-06-09 11:53:33:0630] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:53:38:0048] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:53:38:0595] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:54:49:0899] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:54:50:0414] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:56:41:0601] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:56:42:0104] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:56:46:0844] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:56:46:0858] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:56:46:0894] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2432]
[2020-06-09 11:56:46:0894] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3936]
[2020-06-09 11:57:11:0073] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:57:11:0087] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:57:11:0125] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4944]
[2020-06-09 11:57:11:0125] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[292]
[2020-06-09 11:57:15:0772] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:57:15:0800] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 11:57:15:0862] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 11:58:58:0084] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:58:58:0604] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:58:59:0292] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:58:59:0307] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:58:59:0342] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3408]
[2020-06-09 11:58:59:0343] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2580]
[2020-06-09 11:59:08:0379] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:59:47:0921] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:59:48:0430] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 11:59:49:0140] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:59:49:0153] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:59:49:0189] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4864]
[2020-06-09 11:59:49:0189] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4380]
[2020-06-09 11:59:49:0258] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:59:49:0272] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 11:59:49:0306] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4308]
[2020-06-09 11:59:49:0306] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2972]
[2020-06-09 11:59:56:0155] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 11:59:56:0189] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 11:59:56:0245] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 11:59:57:0306] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 11:59:57:0394] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 13:34:28:0781] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 13:34:29:0305] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-09 13:34:30:0021] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 13:34:30:0036] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 13:34:30:0067] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2600]
[2020-06-09 13:34:30:0068] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3752]
[2020-06-09 13:34:37:0767] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 13:34:37:0780] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-09 13:34:37:0813] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3176]
[2020-06-09 13:34:37:0814] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5216]
[2020-06-09 13:47:26:0481] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-09 13:47:26:0526] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 13:47:26:0571] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-09 13:47:27:0768] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-09 13:47:27:0775] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-23 16:04:55:0215] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 16:04:56:0320] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 16:06:05:0430] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 16:06:05:0958] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 16:06:28:0321] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-23 16:06:28:0339] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-23 16:06:28:0385] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9848]
[2020-06-23 16:06:28:0386] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8216]
[2020-06-23 16:06:34:0141] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-23 16:06:34:0155] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-23 16:06:34:0192] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7504]
[2020-06-23 16:06:34:0192] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10232]
[2020-06-23 16:07:29:0631] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-23 16:07:29:0699] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-23 16:07:29:0721] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-23 16:07:30:0641] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-23 16:07:30:0669] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-23 17:11:33:0271] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 17:11:33:0794] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 17:11:48:0633] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-23 17:11:48:0647] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-23 17:11:48:0684] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2528]
[2020-06-23 17:11:48:0684] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2952]
[2020-06-23 17:11:50:0076] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-23 17:11:50:0090] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-23 17:11:50:0123] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6616]
[2020-06-23 17:11:50:0123] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4928]
[2020-06-23 17:11:52:0919] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-23 17:11:53:0007] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-23 17:11:53:0008] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-23 17:11:53:0510] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-06-23 17:11:53:0542] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-06-23 17:25:50:0870] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-23 17:25:51:0415] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-24 08:17:30:0390] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-24 08:17:30:0930] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-24 08:21:06:0886] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-24 08:21:07:0391] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-24 08:21:21:0934] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:21:21:0947] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-24 08:21:21:0981] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3348]
[2020-06-24 08:21:21:0981] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8260]
[2020-06-24 08:21:23:0895] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:21:23:0907] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-06-24 08:21:23:0942] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10064]
[2020-06-24 08:21:23:0942] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2176]
[2020-06-24 08:22:56:0918] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:23:06:0202] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:23:09:0893] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:23:12:0887] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:25:19:0316] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:25:20:0489] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:40:55:0214] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:40:56:0130] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:44:35:0678] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:44:36:0713] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:44:40:0739] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:44:52:0076] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:46:34:0372] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:46:35:0395] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:46:48:0406] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:46:54:0915] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:49:06:0836] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-24 08:49:13:0412] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-06-26 14:48:29:0264] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:48:29:0773] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:48:46:0624] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:48:47:0117] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:49:49:0603] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:49:50:0095] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:50:32:0631] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:50:33:0115] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:51:26:0931] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:51:27:0408] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:52:30:0301] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:52:43:0370] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:52:43:0850] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:53:17:0064] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:53:17:0533] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:54:05:0436] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:54:05:0926] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:55:30:0000] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:55:30:0473] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:55:50:0531] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 14:55:51:0030] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:00:49:0368] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:00:49:0846] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:01:18:0924] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:01:19:0435] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:01:42:0773] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:01:43:0269] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:02:04:0657] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-06-26 15:02:05:0157] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 02:28:48:0454] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 02:28:49:0287] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 02:32:41:0955] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-25 02:32:41:0972] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-25 02:32:42:0034] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7876]
[2020-07-25 02:32:42:0034] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10296]
[2020-07-25 02:32:43:0240] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-25 02:32:43:0256] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-25 02:32:43:0295] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2304]
[2020-07-25 02:32:43:0296] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3180]
[2020-07-25 02:37:29:0826] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-25 02:37:29:0914] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-25 02:37:29:0924] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-25 02:37:30:0726] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-25 02:37:30:0776] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-25 03:04:05:0794] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 03:04:06:0401] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 03:04:19:0779] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-25 03:04:19:0793] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-25 03:04:19:0831] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8384]
[2020-07-25 03:04:19:0831] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7956]
[2020-07-25 03:05:40:0520] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-25 03:05:40:0534] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-25 03:05:40:0571] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5292]
[2020-07-25 03:05:40:0571] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8624]
[2020-07-25 03:05:53:0933] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 03:05:54:0506] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 03:06:14:0901] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-25 03:06:15:0435] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:17:22:0128] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:17:22:0750] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:17:34:0731] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:17:34:0745] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:17:34:0783] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2752]
[2020-07-28 15:17:34:0783] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9948]
[2020-07-28 15:17:37:0625] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:17:37:0639] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:17:37:0676] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9920]
[2020-07-28 15:17:37:0676] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6100]
[2020-07-28 15:33:08:0042] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:33:08:0583] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:33:17:0115] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:33:17:0129] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:33:17:0164] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8432]
[2020-07-28 15:33:17:0165] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5640]
[2020-07-28 15:34:39:0621] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:34:40:0156] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:34:54:0793] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:34:55:0294] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:35:02:0922] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:35:02:0935] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:35:02:0972] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7860]
[2020-07-28 15:35:02:0972] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5696]
[2020-07-28 15:35:25:0658] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:35:25:0674] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:35:25:0710] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5380]
[2020-07-28 15:35:25:0710] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9684]
[2020-07-28 15:37:43:0264] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:37:43:0814] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:41:18:0653] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:41:19:0161] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:41:29:0836] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:41:29:0849] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:41:29:0886] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9684]
[2020-07-28 15:41:29:0886] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8292]
[2020-07-28 15:42:32:0919] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:42:33:0467] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:42:40:0942] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:42:40:0956] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:42:40:0992] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3840]
[2020-07-28 15:42:40:0993] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8288]
[2020-07-28 15:43:53:0621] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:44:14:0732] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:44:15:0276] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:44:24:0154] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:44:24:0168] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:44:24:0205] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4008]
[2020-07-28 15:44:24:0205] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9796]
[2020-07-28 15:44:36:0886] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:45:09:0543] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:45:10:0084] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:45:21:0833] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:45:21:0846] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:45:21:0884] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9312]
[2020-07-28 15:45:21:0884] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7504]
[2020-07-28 15:45:42:0040] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:46:28:0699] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:46:29:0254] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:46:37:0146] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:46:37:0159] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:46:37:0196] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4780]
[2020-07-28 15:46:37:0196] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5976]
[2020-07-28 15:49:54:0118] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:49:55:0449] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:50:19:0311] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:50:23:0475] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:50:48:0592] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:51:18:0308] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:51:18:0846] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:51:28:0782] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:51:28:0797] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:51:28:0835] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[740]
[2020-07-28 15:51:28:0835] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6112]
[2020-07-28 15:51:42:0385] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:52:02:0996] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:52:03:0539] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:52:12:0167] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:52:12:0182] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:52:12:0220] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9952]
[2020-07-28 15:52:12:0220] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9024]
[2020-07-28 15:52:35:0107] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:52:35:0652] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:52:43:0311] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:52:43:0325] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:52:43:0361] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7704]
[2020-07-28 15:52:43:0362] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3692]
[2020-07-28 15:53:37:0242] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:54:35:0996] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:54:36:0539] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:54:43:0159] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:54:43:0173] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:54:43:0210] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8648]
[2020-07-28 15:54:43:0211] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5976]
[2020-07-28 15:55:01:0111] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:55:23:0277] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:55:23:0817] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:55:32:0342] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:55:32:0356] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:55:32:0393] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5268]
[2020-07-28 15:55:32:0393] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9480]
[2020-07-28 15:56:28:0710] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:56:50:0292] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:56:50:0827] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:56:58:0510] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:56:58:0524] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:56:58:0561] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8900]
[2020-07-28 15:56:58:0561] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9128]
[2020-07-28 15:57:06:0211] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:57:22:0324] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:57:22:0863] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:57:31:0329] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:57:31:0343] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:57:31:0379] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7224]
[2020-07-28 15:57:31:0380] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2732]
[2020-07-28 15:58:18:0136] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:58:18:0149] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:58:18:0187] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9644]
[2020-07-28 15:58:18:0187] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[976]
[2020-07-28 15:58:26:0176] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:58:26:0213] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-28 15:58:26:0266] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-28 15:58:45:0684] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:58:46:0225] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-28 15:58:54:0062] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-28 15:58:54:0076] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-28 15:58:54:0112] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2472]
[2020-07-28 15:58:54:0113] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2168]
[2020-07-28 15:59:06:0254] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:13:59:0074] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:14:12:0080] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:14:49:0277] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:14:49:0851] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:14:57:0172] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:14:57:0185] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:14:57:0219] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4000]
[2020-07-29 08:14:57:0220] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2060]
[2020-07-29 08:14:57:0344] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:14:57:0361] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:14:57:0452] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5200]
[2020-07-29 08:14:57:0452] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7468]
[2020-07-29 08:15:51:0223] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:15:51:0307] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:15:51:0312] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:15:52:0074] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:15:52:0106] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:16:00:0810] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:16:01:0347] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:16:02:0131] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:16:02:0145] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:16:02:0179] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6360]
[2020-07-29 08:16:02:0179] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9160]
[2020-07-29 08:16:02:0256] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:16:02:0271] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:16:02:0314] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2924]
[2020-07-29 08:16:02:0314] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3480]
[2020-07-29 08:16:08:0774] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:16:08:0841] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:16:08:0864] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:16:10:0320] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:16:10:0386] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:16:29:0965] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:16:30:0520] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:16:31:0282] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:16:31:0296] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:16:31:0331] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7732]
[2020-07-29 08:16:31:0331] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8644]
[2020-07-29 08:16:31:0404] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:16:31:0419] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:16:31:0461] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8288]
[2020-07-29 08:16:31:0462] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3856]
[2020-07-29 08:17:38:0261] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:17:38:0810] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:17:39:0587] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:17:39:0602] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:17:39:0634] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3512]
[2020-07-29 08:17:39:0634] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8592]
[2020-07-29 08:17:39:0711] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:17:39:0728] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:17:39:0775] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6672]
[2020-07-29 08:17:39:0776] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2708]
[2020-07-29 08:18:29:0555] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:18:29:0602] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:18:29:0646] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:18:35:0059] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:18:35:0605] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:18:36:0374] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:18:36:0388] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:18:36:0424] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5620]
[2020-07-29 08:18:36:0425] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7812]
[2020-07-29 08:18:39:0060] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:18:39:0074] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:18:39:0111] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7616]
[2020-07-29 08:18:39:0111] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8696]
[2020-07-29 08:22:49:0824] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:22:50:0368] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:22:51:0270] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:22:51:0286] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:22:51:0326] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7704]
[2020-07-29 08:22:51:0326] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8656]
[2020-07-29 08:22:53:0169] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:22:53:0184] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:22:53:0227] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2772]
[2020-07-29 08:22:53:0228] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3396]
[2020-07-29 08:23:01:0265] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:23:01:0312] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:23:01:0355] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:23:05:0886] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:23:06:0432] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:23:08:0735] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:23:08:0751] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:23:08:0791] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[432]
[2020-07-29 08:23:08:0791] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8256]
[2020-07-29 08:23:08:0923] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:23:08:0936] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:23:08:0977] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8724]
[2020-07-29 08:23:08:0978] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1752]
[2020-07-29 08:23:35:0279] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:23:35:0368] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:23:35:0372] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:23:35:0959] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:23:35:0994] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:24:05:0604] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:24:06:0155] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:24:08:0760] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:24:08:0776] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:24:08:0815] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4012]
[2020-07-29 08:24:08:0816] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8080]
[2020-07-29 08:24:08:0940] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:24:08:0955] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:24:08:0997] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6792]
[2020-07-29 08:24:08:0997] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2216]
[2020-07-29 08:25:19:0763] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:25:20:0321] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:25:21:0080] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:25:21:0094] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:25:21:0128] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8716]
[2020-07-29 08:25:21:0129] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4604]
[2020-07-29 08:25:21:0202] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:25:21:0217] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:25:21:0256] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5164]
[2020-07-29 08:25:21:0256] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1980]
[2020-07-29 08:25:32:0247] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:25:32:0248] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:25:32:0336] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:25:32:0968] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:25:32:0983] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:31:07:0432] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:31:07:0979] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:31:22:0381] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:31:22:0395] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:31:22:0430] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4288]
[2020-07-29 08:31:22:0430] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2664]
[2020-07-29 08:31:22:0503] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:31:22:0519] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:31:22:0558] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2216]
[2020-07-29 08:31:22:0559] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4508]
[2020-07-29 08:31:34:0488] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:31:34:0576] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:31:34:0578] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:35:49:0541] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:35:50:0086] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:35:52:0052] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:35:52:0066] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:35:52:0103] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8444]
[2020-07-29 08:35:52:0103] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[428]
[2020-07-29 08:35:52:0234] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:35:52:0248] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:35:52:0288] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1296]
[2020-07-29 08:35:52:0288] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4012]
[2020-07-29 08:40:28:0869] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:40:29:0416] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:40:30:0315] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:40:30:0331] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:40:30:0372] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8724]
[2020-07-29 08:40:30:0372] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4752]
[2020-07-29 08:40:31:0851] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:40:31:0864] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:40:31:0903] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[244]
[2020-07-29 08:40:31:0903] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2312]
[2020-07-29 08:43:55:0884] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:43:56:0438] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:43:57:0227] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:43:57:0245] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:43:57:0289] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5692]
[2020-07-29 08:43:57:0289] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7384]
[2020-07-29 08:43:59:0007] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:43:59:0023] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:43:59:0067] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[128]
[2020-07-29 08:43:59:0068] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1236]
[2020-07-29 08:52:20:0056] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:52:20:0588] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:52:21:0465] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:52:21:0478] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:52:21:0514] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8396]
[2020-07-29 08:52:21:0514] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2136]
[2020-07-29 08:52:30:0867] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:52:36:0743] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:52:36:0756] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:52:36:0801] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3752]
[2020-07-29 08:52:36:0801] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6972]
[2020-07-29 08:53:27:0961] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:53:28:0039] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:53:28:0050] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:53:28:0438] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:53:28:0460] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:54:43:0530] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:54:44:0253] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 08:54:46:0842] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:54:46:0862] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:54:46:0924] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1156]
[2020-07-29 08:54:46:0925] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6428]
[2020-07-29 08:54:47:0071] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:54:47:0088] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 08:54:47:0153] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8568]
[2020-07-29 08:54:47:0154] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1972]
[2020-07-29 08:57:04:0009] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 08:57:04:0021] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 08:57:04:0095] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:57:05:0397] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 08:57:05:0405] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 09:07:40:0715] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 09:07:41:0270] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 09:07:43:0995] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:07:44:0008] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 09:07:44:0049] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4028]
[2020-07-29 09:07:44:0050] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2344]
[2020-07-29 09:07:44:0125] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:07:44:0140] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 09:07:44:0178] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8176]
[2020-07-29 09:07:44:0178] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9856]
[2020-07-29 09:11:14:0575] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 09:11:15:0115] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 09:11:16:0028] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:11:16:0044] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 09:11:16:0089] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10088]
[2020-07-29 09:11:16:0089] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4436]
[2020-07-29 09:11:17:0809] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:11:17:0823] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 09:11:17:0858] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2408]
[2020-07-29 09:11:17:0859] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6736]
[2020-07-29 09:12:02:0072] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:12:02:0142] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 09:12:02:0162] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 09:13:50:0548] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 09:13:51:0134] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 09:13:54:0165] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:13:54:0180] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 09:13:54:0223] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5696]
[2020-07-29 09:13:54:0223] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3296]
[2020-07-29 09:13:54:0296] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 09:13:54:0310] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 09:13:54:0349] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5296]
[2020-07-29 09:13:54:0349] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10204]
[2020-07-29 10:10:38:0377] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:10:38:0929] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:10:39:0755] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:10:39:0770] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:10:39:0808] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9748]
[2020-07-29 10:10:39:0808] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6500]
[2020-07-29 10:10:39:0900] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:10:39:0914] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:10:39:0955] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9472]
[2020-07-29 10:10:39:0955] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1628]
[2020-07-29 10:10:59:0558] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:11:00:0152] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:11:14:0848] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:11:14:0864] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:11:14:0901] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4448]
[2020-07-29 10:11:14:0902] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8484]
[2020-07-29 10:11:14:0980] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:11:14:0996] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:11:15:0032] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9316]
[2020-07-29 10:11:15:0033] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5588]
[2020-07-29 10:11:58:0151] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:11:58:0703] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:12:01:0508] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:12:01:0526] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:12:01:0571] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10228]
[2020-07-29 10:12:01:0571] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2984]
[2020-07-29 10:12:01:0641] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:12:01:0655] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:12:01:0691] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7068]
[2020-07-29 10:12:01:0692] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9860]
[2020-07-29 10:12:36:0651] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:12:37:0566] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:12:38:0737] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:12:38:0751] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:12:38:0787] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3232]
[2020-07-29 10:12:38:0787] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9424]
[2020-07-29 10:12:38:0866] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:12:38:0885] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:12:38:0926] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9360]
[2020-07-29 10:12:38:0926] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3992]
[2020-07-29 10:17:25:0825] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:17:26:0762] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:17:27:0903] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:17:27:0917] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:17:27:0962] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8512]
[2020-07-29 10:17:27:0963] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6136]
[2020-07-29 10:17:28:0034] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:17:28:0051] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:17:28:0095] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7484]
[2020-07-29 10:17:28:0096] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9524]
[2020-07-29 10:28:04:0198] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:28:05:0138] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:28:06:0303] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:28:06:0318] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:28:06:0355] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8296]
[2020-07-29 10:28:06:0355] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3480]
[2020-07-29 10:28:06:0435] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:28:06:0451] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:28:06:0491] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7404]
[2020-07-29 10:28:06:0492] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[648]
[2020-07-29 10:28:55:0905] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:28:56:0818] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:29:00:0011] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:29:00:0029] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:29:00:0066] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4732]
[2020-07-29 10:29:00:0067] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9760]
[2020-07-29 10:29:00:0198] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:29:00:0212] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:29:00:0254] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8484]
[2020-07-29 10:29:00:0254] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8600]
[2020-07-29 10:30:03:0201] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:30:03:0760] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:30:04:0562] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:30:04:0576] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:30:04:0616] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4428]
[2020-07-29 10:30:04:0617] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4752]
[2020-07-29 10:30:04:0697] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:30:04:0711] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:30:04:0748] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[656]
[2020-07-29 10:30:04:0748] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9024]
[2020-07-29 10:30:27:0308] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:30:27:0854] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:30:31:0788] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:30:31:0804] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:30:31:0849] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2312]
[2020-07-29 10:30:31:0849] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5696]
[2020-07-29 10:30:31:0924] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:30:31:0946] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:30:31:0992] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4380]
[2020-07-29 10:30:31:0992] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9360]
[2020-07-29 10:30:59:0963] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:31:00:0505] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:31:01:0334] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:31:01:0348] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:31:01:0384] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4416]
[2020-07-29 10:31:01:0384] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7916]
[2020-07-29 10:31:05:0678] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:31:05:0692] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:31:05:0730] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5988]
[2020-07-29 10:31:05:0730] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2044]
[2020-07-29 10:31:25:0808] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:31:26:0358] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:31:27:0139] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:31:27:0153] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:31:27:0195] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[248]
[2020-07-29 10:31:27:0195] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2200]
[2020-07-29 10:31:27:0272] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:31:27:0288] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:31:27:0326] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8556]
[2020-07-29 10:31:27:0327] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3716]
[2020-07-29 10:33:41:0402] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:33:41:0947] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:33:42:0747] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:33:42:0761] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:33:42:0799] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1232]
[2020-07-29 10:33:42:0800] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9320]
[2020-07-29 10:33:42:0875] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:33:42:0889] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:33:42:0925] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6868]
[2020-07-29 10:33:42:0925] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9992]
[2020-07-29 10:36:12:0339] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:36:12:0880] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:36:13:0644] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:36:13:0658] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:36:13:0695] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2312]
[2020-07-29 10:36:13:0696] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1344]
[2020-07-29 10:36:13:0766] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:36:13:0781] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:36:13:0820] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10104]
[2020-07-29 10:36:13:0820] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9684]
[2020-07-29 10:38:14:0966] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:38:15:0515] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:38:16:0351] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:38:16:0365] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:38:16:0402] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7288]
[2020-07-29 10:38:16:0403] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7332]
[2020-07-29 10:38:21:0625] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:39:08:0869] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:39:09:0408] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:39:15:0609] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:39:15:0625] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:39:15:0670] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2480]
[2020-07-29 10:39:15:0670] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9968]
[2020-07-29 10:39:15:0745] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:39:15:0758] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:39:15:0795] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4788]
[2020-07-29 10:39:15:0795] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8844]
[2020-07-29 10:39:30:0395] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:39:30:0432] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 10:39:30:0484] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 10:42:19:0027] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:42:19:0563] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:42:24:0546] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:42:24:0562] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:42:24:0608] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8256]
[2020-07-29 10:42:24:0609] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9752]
[2020-07-29 10:42:24:0680] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:42:24:0694] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:42:24:0729] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2452]
[2020-07-29 10:42:24:0730] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6884]
[2020-07-29 10:42:35:0106] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:42:35:0670] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:42:36:0490] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:42:36:0504] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:42:36:0540] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3908]
[2020-07-29 10:42:36:0540] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8412]
[2020-07-29 10:42:50:0606] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:42:51:0171] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:42:54:0586] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:42:54:0604] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:42:54:0645] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5536]
[2020-07-29 10:42:54:0646] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1884]
[2020-07-29 10:42:54:0719] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:42:54:0737] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:42:54:0778] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5568]
[2020-07-29 10:42:54:0778] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5688]
[2020-07-29 10:43:31:0059] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:43:31:0608] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:43:34:0545] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:43:34:0565] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:43:34:0610] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8196]
[2020-07-29 10:43:34:0610] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1344]
[2020-07-29 10:43:34:0738] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:43:34:0763] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:43:34:0806] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10220]
[2020-07-29 10:43:34:0806] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8536]
[2020-07-29 10:43:57:0248] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:43:57:0811] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:43:58:0626] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:43:58:0641] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:43:58:0680] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3716]
[2020-07-29 10:43:58:0681] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8744]
[2020-07-29 10:44:24:0965] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:44:25:0527] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:44:28:0206] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:44:28:0220] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:44:28:0257] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5400]
[2020-07-29 10:44:28:0257] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[668]
[2020-07-29 10:44:28:0330] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:44:28:0353] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:44:28:0399] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2880]
[2020-07-29 10:44:28:0399] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[900]
[2020-07-29 10:47:15:0637] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:47:16:0198] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:47:16:0988] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:47:17:0002] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:47:17:0038] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9148]
[2020-07-29 10:47:17:0038] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2200]
[2020-07-29 10:47:17:0112] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:47:17:0127] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:47:17:0164] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10184]
[2020-07-29 10:47:17:0165] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10096]
[2020-07-29 10:48:22:0685] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:48:23:0240] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:48:24:0022] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:48:24:0037] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:48:24:0073] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5540]
[2020-07-29 10:48:24:0073] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6836]
[2020-07-29 10:48:24:0144] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:48:24:0160] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:48:24:0199] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1204]
[2020-07-29 10:48:24:0200] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[260]
[2020-07-29 10:49:45:0997] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:49:46:0543] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 10:49:50:0593] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:49:50:0609] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:49:50:0653] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[96]
[2020-07-29 10:49:50:0654] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2192]
[2020-07-29 10:49:50:0726] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:49:50:0744] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 10:49:50:0787] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6744]
[2020-07-29 10:49:50:0788] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4680]
[2020-07-29 10:51:30:0308] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 10:51:30:0339] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 10:51:30:0398] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 10:51:31:0025] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 10:51:31:0065] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 11:10:02:0620] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:10:03:0171] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:10:06:0581] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:10:06:0598] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:10:06:0637] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1296]
[2020-07-29 11:10:06:0637] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2564]
[2020-07-29 11:10:06:0711] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:10:06:0727] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:10:06:0765] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11176]
[2020-07-29 11:10:06:0766] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7064]
[2020-07-29 11:11:35:0402] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:11:35:0948] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:11:36:0699] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:11:36:0712] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:11:36:0749] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10636]
[2020-07-29 11:11:36:0749] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10632]
[2020-07-29 11:11:36:0824] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:11:36:0841] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:11:36:0886] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2256]
[2020-07-29 11:11:36:0887] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1016]
[2020-07-29 11:12:07:0700] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:12:08:0258] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:12:09:0033] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:12:09:0047] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:12:09:0091] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1864]
[2020-07-29 11:12:09:0091] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2216]
[2020-07-29 11:12:09:0168] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:12:09:0185] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:12:09:0225] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7216]
[2020-07-29 11:12:09:0226] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8852]
[2020-07-29 11:16:47:0012] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:16:47:0597] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:16:48:0419] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:16:48:0433] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:16:48:0471] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4032]
[2020-07-29 11:16:48:0472] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3148]
[2020-07-29 11:16:48:0551] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:16:48:0567] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:16:48:0604] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8160]
[2020-07-29 11:16:48:0605] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4572]
[2020-07-29 11:18:39:0541] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:18:39:0543] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 11:18:39:0630] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 11:19:26:0387] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:19:26:0933] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:19:27:0834] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:19:27:0848] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:19:27:0885] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4244]
[2020-07-29 11:19:27:0885] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9628]
[2020-07-29 11:19:31:0056] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:19:31:0072] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:19:31:0116] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9500]
[2020-07-29 11:19:31:0117] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6092]
[2020-07-29 11:21:29:0488] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:21:29:0538] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 11:21:29:0577] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 11:21:30:0640] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 11:21:30:0662] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 11:21:36:0043] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:21:36:0608] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:21:37:0389] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:21:37:0403] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:21:37:0440] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2772]
[2020-07-29 11:21:37:0440] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7748]
[2020-07-29 11:22:02:0120] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:22:02:0135] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:22:02:0174] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5164]
[2020-07-29 11:22:02:0174] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11224]
[2020-07-29 11:37:38:0339] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:37:38:0877] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:37:39:0710] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:37:39:0725] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:37:39:0762] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5684]
[2020-07-29 11:37:39:0763] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11236]
[2020-07-29 11:37:39:0838] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:37:39:0852] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:37:39:0888] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7640]
[2020-07-29 11:37:39:0889] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9500]
[2020-07-29 11:51:27:0760] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:51:28:0314] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 11:51:29:0116] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:51:29:0130] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:51:29:0167] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9248]
[2020-07-29 11:51:29:0168] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3684]
[2020-07-29 11:51:31:0659] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 11:51:31:0673] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 11:51:31:0717] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8388]
[2020-07-29 11:51:31:0717] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6928]
[2020-07-29 13:33:09:0131] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:33:09:0165] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:33:09:0221] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:33:10:0094] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:33:10:0165] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:34:22:0261] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:34:22:0842] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:34:23:0938] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:34:23:0952] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:34:23:0989] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9312]
[2020-07-29 13:34:23:0989] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4428]
[2020-07-29 13:34:26:0709] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:34:26:0730] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:34:26:0775] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10740]
[2020-07-29 13:34:26:0776] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5480]
[2020-07-29 13:35:37:0772] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:35:37:0861] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:35:37:0873] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:36:09:0512] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:36:10:0048] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:36:10:0923] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:36:10:0939] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:36:10:0983] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10948]
[2020-07-29 13:36:10:0983] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2004]
[2020-07-29 13:36:12:0898] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:36:12:0914] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:36:12:0961] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4436]
[2020-07-29 13:36:12:0962] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5932]
[2020-07-29 13:38:22:0062] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:38:22:0076] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:38:22:0151] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:38:22:0867] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:38:22:0904] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:39:20:0309] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:39:20:0852] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:39:21:0629] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:39:21:0648] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:39:21:0685] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4152]
[2020-07-29 13:39:21:0685] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10512]
[2020-07-29 13:39:24:0574] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:39:24:0587] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:39:24:0633] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10604]
[2020-07-29 13:39:24:0633] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3180]
[2020-07-29 13:39:29:0447] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:39:29:0485] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:39:29:0537] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:39:31:0091] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 13:39:31:0140] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 13:39:56:0463] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:39:57:0007] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:40:14:0793] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:40:15:0311] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 13:40:18:0245] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:40:18:0259] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:40:18:0297] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2312]
[2020-07-29 13:40:18:0297] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6800]
[2020-07-29 13:40:18:0370] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 13:40:18:0386] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 13:40:18:0425] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[144]
[2020-07-29 13:40:18:0426] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7040]
[2020-07-29 13:59:32:0418] |         XmlParse.cpp-L2398: SetNodeValue [TriggerSource], nSdkErrCode[80000206]
[2020-07-29 13:59:32:0418] |         XmlParse.cpp-L2398: SetNodeValue [TriggerMode], nSdkErrCode[80000206]
[2020-07-29 13:59:32:0418] |         XmlParse.cpp-L2891: SetNodeValue [TriggerSoftware], nSdkErrCode[80000206]
[2020-07-29 14:00:03:0793] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:00:03:0806] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 14:00:03:0834] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:00:04:0080] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:00:04:0094] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 14:00:08:0138] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:00:08:0713] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:00:09:0478] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:00:09:0494] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:00:09:0539] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3404]
[2020-07-29 14:00:09:0539] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1464]
[2020-07-29 14:00:09:0615] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:00:09:0630] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:00:09:0674] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[260]
[2020-07-29 14:00:09:0674] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9608]
[2020-07-29 14:08:46:0557] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:08:46:0625] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:08:46:0647] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 14:08:47:0591] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:08:47:0655] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 14:09:44:0198] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:09:44:0741] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:09:48:0332] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:09:48:0345] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:09:48:0381] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6496]
[2020-07-29 14:09:48:0382] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5456]
[2020-07-29 14:09:48:0504] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:09:48:0518] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:09:48:0559] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1084]
[2020-07-29 14:09:48:0559] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3940]
[2020-07-29 14:10:57:0246] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:10:57:0787] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:10:58:0567] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:10:58:0583] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:10:58:0627] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5268]
[2020-07-29 14:10:58:0628] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6108]
[2020-07-29 14:10:58:0701] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:10:58:0722] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:10:58:0766] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9340]
[2020-07-29 14:10:58:0766] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10720]
[2020-07-29 14:11:18:0566] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:11:18:0634] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:11:18:0655] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 14:12:14:0619] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:12:15:0156] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:12:19:0383] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:12:19:0397] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:12:19:0439] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9268]
[2020-07-29 14:12:19:0439] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5052]
[2020-07-29 14:12:19:0568] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:12:19:0582] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:12:19:0623] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2400]
[2020-07-29 14:12:19:0623] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7044]
[2020-07-29 14:13:40:0792] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:13:41:0341] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 14:13:42:0093] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:13:42:0107] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:13:42:0144] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10900]
[2020-07-29 14:13:42:0144] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7488]
[2020-07-29 14:13:42:0215] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:13:42:0228] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 14:13:42:0265] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[376]
[2020-07-29 14:13:42:0265] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10936]
[2020-07-29 14:20:19:0873] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 14:20:19:0910] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:20:19:0962] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 14:20:20:0618] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 14:20:20:0642] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 15:46:33:0326] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 15:46:34:0044] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 15:46:44:0050] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 15:46:44:0064] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 15:46:44:0099] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[652]
[2020-07-29 15:46:44:0100] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6336]
[2020-07-29 15:54:45:0153] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 15:54:45:0696] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 15:54:56:0831] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 15:54:56:0844] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 15:54:56:0878] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9012]
[2020-07-29 15:54:56:0879] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9380]
[2020-07-29 17:03:47:0694] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:03:48:0279] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:04:06:0414] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:04:06:0943] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:04:55:0775] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:04:56:0308] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:05:03:0430] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:05:03:0443] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 17:05:03:0478] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10596]
[2020-07-29 17:05:03:0478] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8332]
[2020-07-29 17:05:09:0938] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:05:09:0952] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 17:05:09:0987] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2876]
[2020-07-29 17:05:09:0988] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5260]
[2020-07-29 17:05:18:0008] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:05:19:0289] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:05:42:0978] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 17:05:43:0041] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 17:06:23:0069] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:06:23:0615] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:06:31:0500] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:06:31:0514] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 17:06:31:0549] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8936]
[2020-07-29 17:06:31:0549] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7852]
[2020-07-29 17:06:32:0875] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:06:32:0888] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 17:06:32:0925] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9244]
[2020-07-29 17:06:32:0925] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10284]
[2020-07-29 17:08:01:0581] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:08:01:0624] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 17:08:01:0670] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 17:08:02:0650] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 17:08:02:0656] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 17:08:46:0074] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:08:46:0649] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-07-29 17:08:53:0178] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:08:53:0191] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 17:08:53:0226] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8524]
[2020-07-29 17:08:53:0226] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10204]
[2020-07-29 17:08:58:0779] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:09:12:0876] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:09:14:0319] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:09:14:0332] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-07-29 17:09:14:0366] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11020]
[2020-07-29 17:09:14:0366] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10032]
[2020-07-29 17:09:24:0846] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:09:46:0896] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-07-29 17:09:46:0918] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 17:09:46:0986] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-07-29 17:09:47:0237] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-07-29 17:09:47:0313] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-08-26 09:08:09:0621] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:08:10:0411] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:08:24:0520] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:08:24:0535] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:08:24:0582] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6720]
[2020-08-26 09:08:24:0582] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6476]
[2020-08-26 09:08:26:0608] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:08:26:0623] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:08:26:0662] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6444]
[2020-08-26 09:08:26:0662] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5728]
[2020-08-26 09:08:37:0335] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:08:37:0400] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-08-26 09:08:37:0424] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-08-26 09:08:37:0943] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-08-26 09:08:37:0979] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-08-26 09:08:56:0159] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:08:56:0724] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:09:16:0348] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:09:16:0920] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:09:54:0231] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:09:54:0753] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:10:05:0409] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:10:05:0423] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:10:05:0459] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6692]
[2020-08-26 09:10:05:0459] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3704]
[2020-08-26 09:10:29:0866] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:10:29:0880] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:10:29:0921] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9996]
[2020-08-26 09:10:29:0921] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10016]
[2020-08-26 09:12:19:0309] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:12:30:0996] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:14:12:0228] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:14:12:0782] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:14:18:0870] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:14:18:0884] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:14:18:0921] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2772]
[2020-08-26 09:14:18:0921] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[428]
[2020-08-26 09:15:06:0834] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:17:01:0991] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:17:02:0558] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:17:10:0543] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:17:10:0556] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:17:10:0592] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6716]
[2020-08-26 09:17:10:0593] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3776]
[2020-08-26 09:18:37:0634] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:18:38:0191] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:18:46:0239] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:18:46:0253] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:18:46:0292] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[664]
[2020-08-26 09:18:46:0292] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4804]
[2020-08-26 09:19:22:0135] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:19:22:0700] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:19:30:0098] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:19:30:0612] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:19:38:0789] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:19:38:0803] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:19:38:0838] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9272]
[2020-08-26 09:19:38:0839] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2848]
[2020-08-26 09:20:01:0254] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:20:01:0819] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:20:08:0167] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:20:08:0181] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:20:08:0218] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9556]
[2020-08-26 09:20:08:0218] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2956]
[2020-08-26 09:20:26:0662] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:20:27:0210] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:20:34:0446] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:20:34:0460] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:20:34:0496] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4436]
[2020-08-26 09:20:34:0496] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8000]
[2020-08-26 09:22:29:0090] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:22:29:0641] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:22:39:0070] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:22:39:0084] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:22:39:0121] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4616]
[2020-08-26 09:22:39:0121] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5412]
[2020-08-26 09:23:13:0722] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:23:14:0879] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:33:25:0405] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:33:25:0964] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:33:36:0214] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:33:36:0227] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:33:36:0264] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2548]
[2020-08-26 09:33:36:0264] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9456]
[2020-08-26 09:33:58:0888] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:33:59:0449] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:34:05:0903] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:34:05:0916] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:34:05:0952] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10140]
[2020-08-26 09:34:05:0953] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10108]
[2020-08-26 09:34:44:0796] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:34:45:0345] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:34:51:0377] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:34:51:0390] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:34:51:0429] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1232]
[2020-08-26 09:34:51:0429] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8280]
[2020-08-26 09:34:53:0609] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:34:53:0623] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:34:53:0659] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9772]
[2020-08-26 09:34:53:0660] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1920]
[2020-08-26 09:35:10:0233] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:35:10:0265] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-08-26 09:35:10:0323] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-08-26 09:35:11:0449] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-08-26 09:35:11:0474] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-08-26 09:38:15:0501] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:38:16:0048] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:38:23:0281] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:38:23:0295] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:38:23:0331] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5484]
[2020-08-26 09:38:23:0331] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7176]
[2020-08-26 09:40:14:0097] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:40:14:0646] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:40:22:0099] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:40:22:0113] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:40:22:0149] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9220]
[2020-08-26 09:40:22:0149] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4496]
[2020-08-26 09:41:09:0875] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:41:10:0427] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:41:16:0180] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:41:16:0193] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:41:16:0230] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10020]
[2020-08-26 09:41:16:0231] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9288]
[2020-08-26 09:41:30:0338] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:41:50:0044] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:41:50:0598] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:41:57:0060] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:41:57:0073] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:41:57:0109] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6528]
[2020-08-26 09:41:57:0110] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9968]
[2020-08-26 09:42:21:0675] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:42:42:0096] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:42:42:0648] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:42:49:0457] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:42:49:0471] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:42:49:0507] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10108]
[2020-08-26 09:42:49:0507] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1504]
[2020-08-26 09:42:52:0145] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:43:26:0908] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:43:27:0474] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:43:47:0538] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:43:47:0552] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:43:47:0588] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8708]
[2020-08-26 09:43:47:0589] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1660]
[2020-08-26 09:43:56:0601] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:44:11:0714] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:44:12:0294] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:44:33:0123] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:44:33:0138] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:44:33:0174] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6152]
[2020-08-26 09:44:33:0175] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8496]
[2020-08-26 09:45:52:0832] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:45:53:0404] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:46:00:0662] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:46:00:0676] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:46:00:0712] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8724]
[2020-08-26 09:46:00:0713] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8184]
[2020-08-26 09:48:07:0703] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:48:18:0625] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:48:19:0186] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:48:26:0301] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:48:26:0315] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:48:26:0352] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9992]
[2020-08-26 09:48:26:0353] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4452]
[2020-08-26 09:48:30:0156] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:49:24:0365] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:49:24:0922] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:51:38:0946] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:51:39:0465] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-08-26 09:51:48:0484] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:51:48:0497] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:51:48:0537] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4024]
[2020-08-26 09:51:48:0537] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5448]
[2020-08-26 09:51:49:0667] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:51:49:0680] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-08-26 09:51:49:0718] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9304]
[2020-08-26 09:51:49:0719] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9056]
[2020-08-26 09:52:15:0013] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-08-26 09:52:15:0061] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-08-26 09:52:15:0102] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-08-26 09:52:15:0537] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-08-26 09:52:15:0566] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 11:30:20:0363] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:30:20:0988] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:30:29:0086] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:30:29:0100] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:30:29:0138] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6500]
[2020-10-09 11:30:29:0138] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11804]
[2020-10-09 11:30:30:0979] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:30:30:0993] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:30:31:0038] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[13084]
[2020-10-09 11:30:31:0039] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8212]
[2020-10-09 11:31:57:0569] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:31:58:0131] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:32:05:0168] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:32:05:0182] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:32:05:0219] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11460]
[2020-10-09 11:32:05:0219] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11316]
[2020-10-09 11:32:21:0247] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:32:21:0806] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:32:28:0221] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:32:28:0236] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:32:28:0276] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4160]
[2020-10-09 11:32:28:0277] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[12320]
[2020-10-09 11:42:18:0527] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:42:19:0076] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:42:26:0510] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:42:26:0524] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:42:26:0563] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[12300]
[2020-10-09 11:42:26:0563] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6696]
[2020-10-09 11:44:02:0245] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:44:02:0798] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:44:09:0419] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:44:09:0435] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:44:09:0485] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[13772]
[2020-10-09 11:44:09:0486] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7236]
[2020-10-09 11:44:46:0294] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:44:46:0851] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:44:53:0226] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:44:53:0240] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:44:53:0277] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8616]
[2020-10-09 11:44:53:0277] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11756]
[2020-10-09 11:48:49:0183] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:48:49:0196] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:48:49:0233] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[14312]
[2020-10-09 11:48:49:0233] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[13220]
[2020-10-09 11:51:39:0380] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:51:39:0444] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 11:51:39:0469] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 11:53:36:0209] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:53:36:0752] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 11:53:42:0628] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:53:42:0641] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:53:42:0678] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10172]
[2020-10-09 11:53:42:0678] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[14156]
[2020-10-09 11:54:00:0237] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 11:54:00:0250] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 11:54:00:0286] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8976]
[2020-10-09 11:54:00:0287] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[11896]
[2020-10-09 15:36:12:0436] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:36:13:0018] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:36:22:0019] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:36:22:0032] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:36:22:0070] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8516]
[2020-10-09 15:36:22:0070] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7936]
[2020-10-09 15:36:39:0437] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:36:39:0451] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:36:39:0487] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5740]
[2020-10-09 15:36:39:0488] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7392]
[2020-10-09 15:41:40:0642] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:41:40:0677] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 15:41:40:0731] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 15:44:15:0050] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:44:15:0573] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:44:24:0896] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:44:24:0910] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:44:24:0947] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6340]
[2020-10-09 15:44:24:0948] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5688]
[2020-10-09 15:44:27:0057] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:44:27:0070] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:44:27:0107] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9564]
[2020-10-09 15:44:27:0107] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[408]
[2020-10-09 15:44:49:0016] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:44:49:0045] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 15:44:49:0106] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 15:44:49:0896] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 15:44:49:0903] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 15:53:14:0276] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:53:14:0782] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:53:25:0807] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:53:25:0820] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:53:25:0857] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7152]
[2020-10-09 15:53:25:0857] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3412]
[2020-10-09 15:53:37:0391] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:53:37:0404] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:53:37:0442] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4904]
[2020-10-09 15:53:37:0442] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9328]
[2020-10-09 15:54:29:0089] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:54:29:0111] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 15:54:29:0178] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 15:54:52:0545] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:54:53:0140] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:55:01:0474] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:55:01:0488] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:55:01:0527] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2128]
[2020-10-09 15:55:01:0527] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3132]
[2020-10-09 15:55:06:0245] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:55:06:0258] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:55:06:0295] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6908]
[2020-10-09 15:55:06:0296] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1716]
[2020-10-09 15:58:13:0293] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:58:13:0857] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:58:22:0616] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:58:22:0629] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:58:22:0665] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3200]
[2020-10-09 15:58:22:0666] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4624]
[2020-10-09 15:58:24:0938] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:58:24:0951] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:58:24:0989] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7340]
[2020-10-09 15:58:24:0989] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6180]
[2020-10-09 15:58:55:0485] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:58:56:0040] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:59:02:0511] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:59:02:0525] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:59:02:0561] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7884]
[2020-10-09 15:59:02:0561] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5688]
[2020-10-09 15:59:04:0441] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:59:04:0454] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:59:04:0491] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9952]
[2020-10-09 15:59:04:0491] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5668]
[2020-10-09 15:59:19:0036] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:59:19:0607] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 15:59:30:0032] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:59:30:0046] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:59:30:0082] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5740]
[2020-10-09 15:59:30:0083] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6284]
[2020-10-09 15:59:31:0773] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 15:59:31:0786] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 15:59:31:0825] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4276]
[2020-10-09 15:59:31:0825] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10032]
[2020-10-09 16:04:12:0541] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:04:12:0593] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 16:04:12:0631] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-09 16:05:18:0889] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 16:05:19:0431] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 16:05:28:0484] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:05:28:0498] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 16:05:28:0534] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10832]
[2020-10-09 16:05:28:0534] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10836]
[2020-10-09 16:05:31:0713] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:05:31:0726] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 16:05:31:0762] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10864]
[2020-10-09 16:05:31:0763] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10868]
[2020-10-09 16:06:09:0683] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 16:06:10:0237] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 16:06:19:0355] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:06:19:0369] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 16:06:19:0405] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6096]
[2020-10-09 16:06:19:0405] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10840]
[2020-10-09 16:06:21:0340] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:06:21:0352] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 16:06:21:0390] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5384]
[2020-10-09 16:06:21:0391] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10816]
[2020-10-09 16:07:50:0312] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 16:07:50:0869] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-09 16:08:13:0861] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:08:13:0876] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 16:08:13:0913] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8812]
[2020-10-09 16:08:13:0913] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[520]
[2020-10-09 16:08:16:0016] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:08:16:0030] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-09 16:08:16:0066] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9928]
[2020-10-09 16:08:16:0066] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10332]
[2020-10-09 16:10:30:0914] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-09 16:10:30:0979] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-09 16:10:31:0003] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-10 11:44:46:0727] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 11:44:47:0373] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 11:45:03:0968] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 11:45:03:0982] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 11:45:04:0015] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7740]
[2020-10-10 11:45:04:0016] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10784]
[2020-10-10 11:45:33:0185] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 11:45:33:0201] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 11:45:33:0243] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4252]
[2020-10-10 11:45:33:0243] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10420]
[2020-10-10 14:15:12:0142] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:15:12:0783] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:15:27:0955] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:15:27:0969] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:15:28:0006] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5812]
[2020-10-10 14:15:28:0006] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7856]
[2020-10-10 14:16:27:0043] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:16:27:0604] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:16:34:0189] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:16:34:0202] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:16:34:0239] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6556]
[2020-10-10 14:16:34:0240] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2748]
[2020-10-10 14:17:18:0806] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:17:18:0819] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:17:18:0856] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1440]
[2020-10-10 14:17:18:0856] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4268]
[2020-10-10 14:19:04:0590] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:19:05:0149] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:19:18:0879] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:19:18:0892] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:19:18:0929] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7828]
[2020-10-10 14:19:18:0929] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6388]
[2020-10-10 14:19:54:0931] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:19:54:0944] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:19:54:0980] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5272]
[2020-10-10 14:19:54:0980] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9580]
[2020-10-10 14:20:28:0382] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:20:28:0464] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-10 14:20:28:0471] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-10 14:20:49:0444] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:20:50:0024] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:20:56:0208] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:20:56:0222] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:20:56:0259] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2812]
[2020-10-10 14:20:56:0260] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10056]
[2020-10-10 14:21:27:0344] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:21:27:0357] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:21:27:0395] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9464]
[2020-10-10 14:21:27:0395] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10668]
[2020-10-10 14:21:36:0193] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:21:36:0270] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-10 14:21:36:0283] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-10 14:23:07:0884] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:23:08:0450] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:23:17:0854] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:23:17:0868] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:23:17:0904] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[5252]
[2020-10-10 14:23:17:0904] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8368]
[2020-10-10 14:23:20:0758] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:23:20:0771] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:23:20:0809] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10828]
[2020-10-10 14:23:20:0809] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10804]
[2020-10-10 14:30:14:0852] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:30:14:0917] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-10 14:30:14:0942] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-10 14:35:49:0744] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:35:50:0302] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:35:57:0642] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:35:57:0655] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:35:57:0691] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4628]
[2020-10-10 14:35:57:0692] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7028]
[2020-10-10 14:35:59:0628] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:35:59:0641] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:35:59:0677] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6480]
[2020-10-10 14:35:59:0677] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1888]
[2020-10-10 14:37:31:0417] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:37:31:0506] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-10-10 14:37:31:0508] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-10-10 14:42:00:0662] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:42:01:0243] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:42:10:0160] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:42:10:0173] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 14:42:10:0210] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2084]
[2020-10-10 14:42:10:0210] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4896]
[2020-10-10 14:42:35:0169] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 14:46:51:0700] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 14:46:52:0247] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 15:47:13:0147] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 15:47:13:0743] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 15:47:20:0091] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 15:47:20:0104] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 15:47:20:0141] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2052]
[2020-10-10 15:47:20:0141] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[548]
[2020-10-10 16:46:41:0059] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:46:41:0562] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:46:50:0935] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:46:50:0949] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:46:50:0986] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4264]
[2020-10-10 16:46:50:0986] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9456]
[2020-10-10 16:48:00:0789] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:48:01:0351] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:48:09:0062] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:48:09:0075] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:48:09:0111] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[4868]
[2020-10-10 16:48:09:0112] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2256]
[2020-10-10 16:49:53:0329] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:49:53:0872] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:50:00:0439] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:50:00:0452] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:50:00:0488] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8]
[2020-10-10 16:50:00:0489] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6312]
[2020-10-10 16:51:35:0804] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:51:36:0337] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:51:42:0916] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:51:42:0930] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:51:42:0967] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11440]
[2020-10-10 16:51:42:0967] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1812]
[2020-10-10 16:52:22:0329] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:52:22:0858] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:52:29:0457] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:52:29:0471] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:52:29:0507] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9724]
[2020-10-10 16:52:29:0507] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9520]
[2020-10-10 16:53:18:0508] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:53:19:0046] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:53:25:0903] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:53:25:0916] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:53:25:0956] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3904]
[2020-10-10 16:53:25:0956] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10012]
[2020-10-10 16:55:19:0666] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:55:20:0223] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:55:27:0932] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:55:27:0947] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:55:27:0986] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2176]
[2020-10-10 16:55:27:0987] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9856]
[2020-10-10 16:56:38:0801] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:56:39:0365] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:56:46:0235] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:56:46:0248] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:56:46:0284] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[12052]
[2020-10-10 16:56:46:0284] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3964]
[2020-10-10 16:57:25:0971] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:57:26:0543] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:57:33:0707] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:57:33:0721] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:57:33:0757] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1272]
[2020-10-10 16:57:33:0757] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[836]
[2020-10-10 16:58:47:0444] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:58:47:0978] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 16:58:55:0598] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 16:58:55:0611] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 16:58:55:0648] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7112]
[2020-10-10 16:58:55:0648] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8496]
[2020-10-10 17:02:04:0848] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:02:05:0415] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:02:12:0119] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 17:02:12:0133] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 17:02:12:0169] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8440]
[2020-10-10 17:02:12:0169] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9456]
[2020-10-10 17:03:05:0764] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:03:06:0299] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:03:15:0162] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 17:03:15:0175] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 17:03:15:0211] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7256]
[2020-10-10 17:03:15:0211] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10352]
[2020-10-10 17:04:22:0786] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:04:23:0342] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:04:30:0279] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 17:04:30:0292] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 17:04:30:0328] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[508]
[2020-10-10 17:04:30:0328] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[2620]
[2020-10-10 17:05:28:0110] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:05:28:0659] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:05:35:0281] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 17:05:35:0294] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 17:05:35:0331] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[3748]
[2020-10-10 17:05:35:0331] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1980]
[2020-10-10 17:06:26:0478] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 17:06:40:0457] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:06:40:0999] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-10 17:06:47:0622] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-10 17:06:47:0636] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-10-10 17:06:47:0672] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[2908]
[2020-10-10 17:06:47:0672] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10000]
[2020-10-10 17:07:40:0955] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-10-13 15:51:28:0788] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-13 15:51:29:0368] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-13 15:54:25:0524] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-13 15:54:26:0085] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-13 16:04:30:0675] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-10-13 16:04:31:0251] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:31:38:0544] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:31:39:0115] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:31:48:0280] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 14:31:48:0294] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-11-24 14:31:48:0335] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[32]
[2020-11-24 14:31:48:0336] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[4124]
[2020-11-24 14:38:48:0052] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:38:48:0619] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:39:16:0870] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:39:17:0406] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:39:26:0709] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:39:27:0234] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:40:55:0712] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:40:56:0247] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 14:41:09:0381] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 14:41:09:0395] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-11-24 14:41:09:0436] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7476]
[2020-11-24 14:41:09:0436] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[13548]
[2020-11-24 14:41:19:0186] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 15:13:00:0392] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 15:13:00:0964] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 15:13:09:0541] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 15:13:09:0555] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-11-24 15:13:09:0591] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[14160]
[2020-11-24 15:13:09:0591] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10808]
[2020-11-24 15:13:21:0198] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 15:13:21:0211] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-11-24 15:13:21:0248] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11640]
[2020-11-24 15:13:21:0248] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7464]
[2020-11-24 15:15:45:0873] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 15:15:46:0444] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-11-24 15:15:53:0308] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 15:15:53:0321] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-11-24 15:15:53:0357] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[10396]
[2020-11-24 15:15:53:0358] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[7548]
[2020-11-24 15:24:59:0259] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 15:24:59:0273] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-11-24 15:24:59:0309] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[13940]
[2020-11-24 15:24:59:0309] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[10964]
[2020-11-24 15:25:46:0996] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-11-24 15:25:47:0072] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-11-24 15:25:47:0085] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-12-05 15:28:52:0391] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:28:52:0927] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:29:31:0798] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:29:32:0339] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:29:52:0797] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:29:53:0326] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:30:10:0860] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:30:10:0874] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:30:10:0911] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[7780]
[2020-12-05 15:30:10:0911] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6732]
[2020-12-05 15:30:12:0040] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:30:12:0054] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:30:12:0093] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1792]
[2020-12-05 15:30:12:0093] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9288]
[2020-12-05 15:31:31:0308] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:31:31:0376] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-12-05 15:31:31:0397] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-12-05 15:40:52:0314] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:40:52:0873] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:41:34:0345] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:41:34:0899] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:43:03:0283] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:43:03:0816] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:44:43:0928] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:44:44:0463] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:44:51:0589] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:44:51:0602] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:44:51:0650] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8184]
[2020-12-05 15:44:51:0650] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5660]
[2020-12-05 15:46:53:0845] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:46:54:0381] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:47:01:0678] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:47:01:0691] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:47:01:0731] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[6012]
[2020-12-05 15:47:01:0731] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1248]
[2020-12-05 15:50:03:0969] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:50:04:0534] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:50:11:0507] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:50:11:0520] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:50:11:0557] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[1808]
[2020-12-05 15:50:11:0557] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6176]
[2020-12-05 15:52:14:0641] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:52:15:0199] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:52:24:0481] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:52:24:0494] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:52:24:0536] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8716]
[2020-12-05 15:52:24:0537] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[1620]
[2020-12-05 15:55:07:0190] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:55:07:0721] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:55:19:0459] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:55:19:0473] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:55:19:0509] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[212]
[2020-12-05 15:55:19:0509] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[6652]
[2020-12-05 15:56:50:0517] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:56:51:0076] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-05 15:56:57:0521] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:56:57:0534] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:56:57:0570] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9356]
[2020-12-05 15:56:57:0571] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[9712]
[2020-12-05 15:57:35:0893] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:57:35:0907] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-05 15:57:35:0957] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[8776]
[2020-12-05 15:57:35:0957] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[8332]
[2020-12-05 15:58:10:0211] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-05 15:58:10:0302] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-12-05 15:58:10:0310] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-12-09 16:08:33:0429] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-09 16:08:33:0999] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-09 16:09:17:0618] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-09 16:09:17:0631] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-09 16:09:17:0668] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9484]
[2020-12-09 16:09:17:0668] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[5296]
[2020-12-09 16:09:50:0812] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-09 16:09:50:0825] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-09 16:09:50:0862] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9248]
[2020-12-09 16:09:50:0862] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[3744]
[2020-12-09 16:21:24:0991] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-09 16:21:25:0025] | CInterfaceManager.cpp-L4384: DisplayProcess exit
[2020-12-09 16:21:25:0081] | CInterfaceManager.cpp-L4591: RecvThreadProcess exit
[2020-12-16 15:18:19:0919] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:18:20:0612] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:18:29:0187] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-16 15:18:29:0201] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-16 15:18:29:0234] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[16548]
[2020-12-16 15:18:29:0235] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[17060]
[2020-12-16 15:21:19:0824] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:21:20:0339] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:21:28:0037] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-16 15:21:28:0051] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-16 15:21:28:0085] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[18796]
[2020-12-16 15:21:28:0085] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[14772]
[2020-12-16 15:24:09:0570] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:24:10:0103] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:24:15:0968] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-16 15:24:15:0982] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-16 15:24:16:0017] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[9816]
[2020-12-16 15:24:16:0017] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[17048]
[2020-12-16 15:37:50:0179] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:37:50:0726] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:38:05:0873] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-16 15:38:05:0887] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-16 15:38:05:0921] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[18240]
[2020-12-16 15:38:05:0921] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[12256]
[2020-12-16 15:40:31:0461] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-16 15:40:31:0475] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-16 15:40:31:0515] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[16680]
[2020-12-16 15:40:31:0515] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[18892]
[2020-12-16 15:46:20:0898] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:46:21:0451] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:47:38:0819] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:47:39:0331] | CInterfaceManager.cpp-L4755: CamCtrl[2040003], GigE SDKVersion(2040003), DriverVersion(2040003)
[2020-12-16 15:47:46:0741] | CInterfaceManager.cpp-L1267: getfcnt [0], getfcntsucceed[0], totalRecv[0] 
[2020-12-16 15:47:46:0755] | CInterfaceManager.cpp-L1122: remalloc RecvBuf succeed, [10554368]
[2020-12-16 15:47:46:0789] | CInterfaceManager.cpp-L4396: Camctrl RecvThread Start, ThreadId[11516]
[2020-12-16 15:47:46:0790] | CInterfaceManager.cpp-L4226: Camctrl DisplayThread Start, ThreadId[14780]

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CamprocessModel
{
    /// <summary>
    ///  
    /// 外链字段使用[Computed]标记
    /// </summary>
    
    public class camright 
    {
        /// <summary>
        /// 必填，不能为空
        /// 
        /// </summary>
       
        public long id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string productcode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double score { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double numlevel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double minangle { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double angelstep { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double maxangle { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double mincontrast { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double highcontrast { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double lowcontrast { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double minsize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double Cmx { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double Cmy { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double Cma { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_1x { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_1y { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_1a { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_1l1 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_1l2 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_2x { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_2y { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_2a { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_2l1 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double r2_2l2 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double x1 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double y1 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double x2 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double y2 { get; set; }
    } 
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using MvCamCtrl.NET;

using DAL;
using System.Runtime.InteropServices;
using System.IO;
using HalconDotNet;
using System.Drawing;

namespace DAL
{
    /// <summary>
    /// 根据海康Sdk以及其cameraOperator接口类编写的相机类的服务方法类
    /// </summary>
  public  class CameraHalconServers
    {
      /// <summary>
      /// 事件绑定主界面显示
      /// </summary>
      public event Action<Log> LogDisplayEvent;
      /// <summary>
      /// 类中调用事件函数
      /// </summary>
      /// <param name="objLog"></param>
      protected virtual void LogDisplayEventProgress(Log objLog)  
      {
          if (LogDisplayEvent != null)
          {
              this.LogDisplayEvent(objLog);
          }
      }

        #region 【 全 局 变 量 】

              public UInt32 m_nBufSizeForDriver = 3840 * 2748 * 3;

              public UInt32 m_nBufSizeForSaveImage = 3840 * 2748 * 3 * 3 + 2048;

              /// <summary>
              /// 用于从驱动获取图像的缓存的数组
              /// </summary>
              public byte[] m_pBufForDriver = new byte[3840 * 2748 * 3];

              /// <summary>
              /// 用于保存图像的缓存的数组
              /// </summary>
              public byte[] m_pBufForSaveImage = new byte[3840 * 2748 * 3 * 3 + 2048];         // 用于保存图像的缓存

              /// <summary>
              /// 用来列举相机的list
              /// </summary>
              public MyCamera.MV_CC_DEVICE_INFO_LIST m_pDeviceList;

              /// <summary>
              /// 海康Sdk的dll的，延伸类
              /// </summary>
              public CameraOperator m_pOperator1;

              /// <summary>
              /// 采集的标志位
              /// </summary>
              public bool m_bGrabbing;

              /// <summary>
              /// 打开的标志位
              /// </summary>
              public bool m_bOpen;

             

        #endregion

        #region 【 基 本 操 作 】

              /// <summary>
              /// 类CameraHalconServers的初始化，会自动初始化类CameraOperator，并进行枚举设备
              /// </summary>
              public CameraHalconServers()
              {
                  m_pDeviceList = new MyCamera.MV_CC_DEVICE_INFO_LIST();
                  m_pOperator1 = new CameraOperator();
                  DeviceListAcq();  //进行枚举设备
                  m_bGrabbing = false;
                  m_bOpen = false;
              }

             
              /// <summary>
              /// 根据CameraOperator类来对连接在线的相机进行枚举
              /// </summary>
              public void DeviceListAcq()
              {
                  int nRet;
                  /*创建设备列表*/
                  System.GC.Collect();
                  nRet = CameraOperator.EnumDevices(MyCamera.MV_GIGE_DEVICE | MyCamera.MV_USB_DEVICE, ref m_pDeviceList);
                  if (0 != nRet)
                  {
                      //LogHelper.WriteLog("枚举设备失败!");
                      LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, "相机枚举设备失败!"));
                      return;
                  }
              }

              /// <summary>
              /// 根据序列index在枚举出来的中的列表，选择相机打开//默认连续模式
              /// </summary>
              /// <param name="objCameraHalcon">Camera类对象</param>
              /// <returns></returns>
              public bool bnOpen(CameraHalcon objCameraHalcon)
              {
                  int nRet = -1;
                  try
                  {
                      DeviceListAcq();

                      //获取选择的设备信息
                      MyCamera.MV_CC_DEVICE_INFO device = (MyCamera.MV_CC_DEVICE_INFO)Marshal.PtrToStructure(m_pDeviceList.pDeviceInfo[objCameraHalcon.CameraIndex], typeof(MyCamera.MV_CC_DEVICE_INFO));

              
                      //打开设备
                      nRet = m_pOperator1.Open(ref device);
                      if (MyCamera.MV_OK != nRet)
                      {
                          LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "设备打开失败!"));
                          //LogHelper.WriteLog(objCameraHalcon.CameraName + "设备打开失败!");
                          return  m_bOpen= false;
                      }
                      else
                      {
                          m_pOperator1.SetEnumValue("AcquisitionMode", 2);// 工作在连续模式
                          m_pOperator1.SetEnumValue("TriggerMode", 0);    // 连续模式
                         
                          LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "设备打开成功!"));
                        
                          return m_bOpen = true;
                      }
                  }
                  catch (Exception ex)
                  {
                      LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "设备打开异常!" + ex.Message));
                      //LogHelper.WriteLog("类CameraHalconServers中方法Open异常：" + ex.Message);
                      return m_bOpen = false;
                  }
              }


              /// <summary>
              /// 设置为IO触发// 相机打开成功后，才能设置，在采集之前
              /// </summary>
              /// <param name="objCameraHalcon">判断没有打开相机后用对象进行打开</param>
              public bool SetIoMode(CameraHalcon objCameraHalcon)
              {
                  if (m_bOpen == false)
                  { bnOpen(objCameraHalcon); }
                   if (m_bOpen == true)
                  {
                      //m_pOperator1.SetEnumValue("AcquisitionMode", 2);// 采集模式，0单帧、1多帧、2连续
                      //m_pOperator1.SetEnumValue("TriggerMode", 1);    // 触发模式
                      //m_pOperator1.SetEnumValue("TriggerSource", 0);  //触发源 0/Line0默认io触发  7/软触发，软件触发
                      if (m_pOperator1.SetEnumValue("AcquisitionMode", 2) + m_pOperator1.SetEnumValue("TriggerMode", 1) + m_pOperator1.SetEnumValue("TriggerSource", 0) == 0)
                      { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "设为IO触发成功!")); return true; }
                      else
                      { m_bOpen = false; LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "设为IO触发失败!")); return false; }
                     
                  }
                   else
                   { return false; }
              }

              /// <summary>
              /// 设置为软触发// 相机打开成功后，才能设置，在采集之前
              /// </summary>
              /// <param name="objCameraHalcon">判断没有打开相机后的用对象进行打开</param>
              public bool SetSoftMode(CameraHalcon objCameraHalcon)
              {
                  if (m_bOpen == false)
                  { bnOpen(objCameraHalcon); }
                  if (m_bOpen == true)
                  {
                      //m_pOperator1.SetEnumValue("AcquisitionMode", 2);// 采集模式，0单帧、1多帧、2连续
                      //m_pOperator1.SetEnumValue("TriggerMode", 1);    // 触发模式
                      //m_pOperator1.SetEnumValue("TriggerSource", 7);  //触发源 0/Line0默认io触发  7/软触发，软件触发
                      if (m_pOperator1.SetEnumValue("AcquisitionMode", 2) + m_pOperator1.SetEnumValue("TriggerMode", 1) + m_pOperator1.SetEnumValue("TriggerSource", 7) == 0)
                      { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "设为软触发OK!"));  return true; }
                      else
                      { m_bOpen = false; LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "设为软触发失败!"));  return false; }
                  }
                  else
                  {  return false; }
              }


              /// <summary>
              /// 设置相机采图模式为连续模式，配合RealtimeDisplay，可实现，在窗体中实时画面
              /// </summary>
              /// <param name="objCameraHalcon">判断没有打开相机后的用对象进行打开</param>
              /// <returns></returns>
              public bool SetContinuesMode(CameraHalcon objCameraHalcon)
              {
                  if (m_bOpen == false)
                  { bnOpen(objCameraHalcon); }
                  if (m_bOpen == true)
                  {
                      if (m_pOperator1.SetEnumValue("AcquisitionMode", 2) == 0)// 采集模式，0单帧、1多帧、2连续
                      {
                          if (MyCamera.MV_OK != m_pOperator1.SetEnumValue("TriggerMode", 0))
                          {  return false; }
                          else {return true; }
                      }
                      else {  m_bOpen = false; return false; }
                  }
                  else { return false; }
              }

              /// <summary>
              /// 相机//开始采集，采集前设置应完好
              /// </summary>
              /// <param name="objCameraHalcon">判断没有打开相机后用对象进行打开</param>
              /// <returns></returns>
              public bool StartGrap(CameraHalcon objCameraHalcon)
              {
                  int nRet;
                  if (m_bOpen == false)
                  { bnOpen(objCameraHalcon); }
                  if (m_bOpen == true)
                  {
                      nRet = m_pOperator1.StartGrabbing();
                      if (MyCamera.MV_OK != nRet)
                      {
                          LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "采集失败!"));
                          //LogHelper.WriteLog("设备取流失败!");
                          m_bGrabbing = false;
                      }
                      else { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "开始采集!")); m_bGrabbing = true; }
                  }
                 else
                 { m_bGrabbing = false; }
                  return m_bGrabbing;
              }

              /// <summary>
              /// 绑定显示控件，绑定后，相机为触发模式话，在触发后会将图像显示在控件上，若相机为连续模式的话，则会实时显示在图像上
              /// </summary>
              /// <param name="objCameraHalcon">Camera类对象</param>
              /// <param name="hWd">显示控件的句柄指针</param>
              /// <returns></returns>
              public bool RealtimeDisplay(CameraHalcon objCameraHalcon, IntPtr hWd)
              {
                  if (m_bGrabbing == false)
                  { StartGrap(objCameraHalcon); }  //判断相机是否在采集
                  if (m_bGrabbing == true)
                  {
                      if (MyCamera.MV_OK == m_pOperator1.Display(hWd))  //实时采集显现
                      { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "开始实时显示!")); return true; }
                      else { m_bGrabbing = false; LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "实时显示失败!")); return false; }
                  }
                  else
                  { return false; }
                
              }


              /// <summary>
              /// 获取该相机的当前曝光参数，参数是objCameraHalcon和相机类别根据类属性Cameratype确认
              /// </summary>
              /// <param name="objCameraHalcon">相机类对象</param>
              /// <returns></returns>
              public float Get_ExposureTime(CameraHalcon objCameraHalcon)
              {
                  float ExposureTimeAbs = -1; //默认为-1 ，可根据-1，来判断设置是否成功
                  if (m_bOpen == false)
                  { bnOpen(objCameraHalcon); }

                  if (m_bOpen == true)
                  {
                      if (objCameraHalcon.Cameratype == 1)  //康耐视 Basler
                      { m_pOperator1.GetFloatValue("ExposureTimeAbs", ref ExposureTimeAbs); }
                      else if (objCameraHalcon.Cameratype == 0)  //海康
                      { m_pOperator1.GetFloatValue("ExposureTime", ref ExposureTimeAbs); }
                  }
                  return ExposureTimeAbs; 
              }

              /// <summary>
              /// 获取该相机的当前增益参数，参数是objCameraHalcon和相机类别根据类属性Cameratype确认
              /// </summary>
              /// <param name="objCameraHalcon">相机类对象</param>
              /// <returns></returns>
              public float Get_Gain(CameraHalcon objCameraHalcon)
              {
                  float GainRaw = -1; //默认为-1 ，可根据-1，来判断设置是否成功
                  if (m_bOpen == false)
                  { bnOpen(objCameraHalcon); }

                  if (m_bOpen == true)
                  {
                      if (objCameraHalcon.Cameratype == 1)  //康耐视 Basler
                      { m_pOperator1.GetFloatValue("GainRaw", ref GainRaw); }

                      else if (objCameraHalcon.Cameratype == 0)  //海康
                      { m_pOperator1.GetFloatValue("Gain", ref GainRaw); }
                  }
                  return GainRaw;
              }


              /// <summary>
              /// 设置该相机的当前曝光参数，可以使用CameraHalcon对象中的属性ExposureTime1/2/3做参数，也可以自行添加
              /// </summary>
              /// <param name="objCameraHalcon">相机类对象</param>
              /// <param name="exposureTime">可以使用对象中事先的属性，也可自行添加</param>
              /// <returns></returns>
              public bool Set_ExposureTime(CameraHalcon objCameraHalcon, float exposureTime)
              {
                    if (m_bGrabbing == false)
                    { StartGrap(objCameraHalcon); }

                    if (m_bGrabbing == true)
                    { 
                        m_pOperator1.SetEnumValue("ExposureAuto", 0);  //自动曝光模式 /0--OFF /1:Once /2.Continuous

                        if (objCameraHalcon.Cameratype == 1)    //康耐视
                        {
                            if (CameraOperator.CO_OK == m_pOperator1.SetFloatValue("ExposureTimeAbs", exposureTime))
                            {
                                //LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "曝光设置成功!" + exposureTime.ToString())); 
                                return true;
                             }
                            else { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "曝光设置失败!" + exposureTime.ToString())); return false; }
                        }
                        else if (objCameraHalcon.Cameratype == 0)   //海康
                        {
                            if (CameraOperator.CO_OK == m_pOperator1.SetFloatValue("ExposureTime", exposureTime))
                            { 
                                //LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "曝光设置成功!" + exposureTime.ToString()));
                                return true; 
                            }
                            else { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "曝光设置失败!" + exposureTime.ToString())); return false; }
                        }
                        else { m_bGrabbing = false; return false; }
                    }
                    else { return false; }
              }


              /// <summary>
              /// 设置该相机的当前增益参数，可以使用CameraHalcon对象中的属性Rain做参数，也可以自行添加
              /// </summary>
              /// <param name="objCameraHalcon">相机类对象</param>
              /// <param name="GainRaw">可以使用对象中事先的属性，也可自行添加</param>
              /// <returns></returns>
              public bool Set_Gain(CameraHalcon objCameraHalcon, float  GainRaw)
              {
                    if (m_bGrabbing == false)
                    { StartGrap(objCameraHalcon); }
                    if (m_bGrabbing == true)
                    {
                        m_pOperator1.SetEnumValue("GainAuto", 0);  //自动增益模式 /0--OFF /1:Once /2.Continuous

                        if (objCameraHalcon.Cameratype == 1)  //康耐视
                        {
                            if (CameraOperator.CO_OK == m_pOperator1.SetFloatValue("GainRaw", GainRaw))
                            { 
                                //LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "增益设置成功!" + GainRaw.ToString()));
                                return true; 
                            }
                            else 
                            {
                                LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "增益设置失败!" + GainRaw.ToString()));
                                m_bGrabbing = false;
                                return false; 
                            }
                        }
                        else if (objCameraHalcon.Cameratype == 0)   //海康
                        {
                            if (CameraOperator.CO_OK == m_pOperator1.SetFloatValue("Gain", GainRaw))
                            { 
                                //LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "增益设置成功!" + GainRaw.ToString())); 
                                return true;
                            }
                            else { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "增益设置失败!" + GainRaw.ToString())); m_bGrabbing = false; return false; }
                        }
                        else { return false; }
                    }
                    else { return false; }
              }


              /// <summary>
              /// 通过sdk取一帧图像，然后将数据流转为Hobject返回，参数objcameracolor在objCameraHalcon中也有，
              /// 单独放出只是为了方便使用多样化，也可以直接将objcameraHalcon中的属性代入
              /// </summary>
              /// <param name="objCameraHalcon">相机类对象</param>
              /// <param name="objcameracolor">相机采图颜色的枚举类，Gary/灰度，Grb/彩色，R/彩色中的红色通道</param>
              /// <returns></returns>
              public HObject SaveImage(CameraHalcon objCameraHalcon,CameraColorEnum objcameracolor)
              {
                  HObject image = null;
                  objCameraHalcon.Image1 = image;
                  if (m_bGrabbing == false)
                  { StartGrap(objCameraHalcon); }
                  if (m_bGrabbing == true)
                  {
                      UInt32 nPayloadSize = 0;
                      if (MyCamera.MV_OK != m_pOperator1.GetIntValue("PayloadSize", ref nPayloadSize))
                      { m_bGrabbing = false; return image; }

                      if (nPayloadSize + 2048 > m_nBufSizeForDriver)
                      {
                          m_nBufSizeForDriver = nPayloadSize + 2048;
                          m_pBufForDriver = new byte[m_nBufSizeForDriver];

                          // 同时对保存图像的缓存做大小判断处理
                          // BMP图片大小：width * height * 3 + 2048(预留BMP头大小)
                          m_nBufSizeForSaveImage = m_nBufSizeForDriver * 3 + 2048;
                          m_pBufForSaveImage = new byte[m_nBufSizeForSaveImage];
                      }
                      IntPtr pData = Marshal.UnsafeAddrOfPinnedArrayElement(m_pBufForDriver, 0);
                      UInt32 nDataLen = 0;
                      MyCamera.MV_FRAME_OUT_INFO_EX stFrameInfo = new MyCamera.MV_FRAME_OUT_INFO_EX();
                      //超时获取一帧，超时时间为1.2秒
                      if (MyCamera.MV_OK != m_pOperator1.GetOneFrameTimeout(pData, ref nDataLen, m_nBufSizeForDriver, ref stFrameInfo, 1200))
                      { LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "取像失败!" ));  return image; }
                      m_pBufForSaveImage = new byte[nDataLen * 3 + 2048];
                      IntPtr pImage = Marshal.UnsafeAddrOfPinnedArrayElement(m_pBufForSaveImage, 0);
                      MyCamera.MV_SAVE_IMAGE_PARAM_EX stSaveParam = new MyCamera.MV_SAVE_IMAGE_PARAM_EX();
                      stSaveParam.enImageType = MyCamera.MV_SAVE_IAMGE_TYPE.MV_Image_Bmp;
                      stSaveParam.enPixelType = stFrameInfo.enPixelType;
                      stSaveParam.pData = pData;
                      stSaveParam.nDataLen = stFrameInfo.nFrameLen;
                      stSaveParam.nHeight = stFrameInfo.nHeight;
                      stSaveParam.nWidth = stFrameInfo.nWidth;
                      stSaveParam.pImageBuffer = pImage;
                      stSaveParam.nBufferSize = nDataLen * 3 + 2048;
                      stSaveParam.nJpgQuality = 80;
                      if (MyCamera.MV_OK != m_pOperator1.SaveImage(ref stSaveParam))
                      { m_bGrabbing = false; return image; }
                      try
                      {
                          int bytewidth = (stFrameInfo.nWidth * 3 + 3) / 4 * 4;  //彩色
                          int bytewidthg = (stFrameInfo.nWidth + 3) / 4 * 4;    //灰度

                          byte[] imagedata = new byte[stFrameInfo.nWidth * stFrameInfo.nHeight * 3];
                          byte[] imagedataR = new byte[stFrameInfo.nWidth * stFrameInfo.nHeight];
                          byte[] imagedataG = new byte[stFrameInfo.nWidth * stFrameInfo.nHeight];
                          byte[] imagedataB = new byte[stFrameInfo.nWidth * stFrameInfo.nHeight];

                              IntPtr pR1 = new IntPtr();
                              IntPtr pG1 =  new IntPtr();
                              IntPtr pB1 = new IntPtr();

                          switch (objcameracolor)
                          {
                              case CameraColorEnum.Gray:

                                  HOperatorSet.GenImage1(out image, "byte", stFrameInfo.nWidth, stFrameInfo.nHeight, pData);
                                
                                  break;
                              case CameraColorEnum.Rgb:

                                    for (int j = 0; j < stFrameInfo.nHeight; j++)
                                    {
                                       for (int i = 0; i < stFrameInfo.nWidth; i++)
                                        {
                                           imagedataB[j * bytewidthg + i] = m_pBufForSaveImage[(stFrameInfo.nHeight - j - 1) * (bytewidth) + i * 3 + 54];
                                           imagedataG[j * bytewidthg + i] = m_pBufForSaveImage[(stFrameInfo.nHeight - j - 1) * (bytewidth) + i * 3 + 1 + 54];
                                           imagedataR[j * bytewidthg + i] = m_pBufForSaveImage[(stFrameInfo.nHeight - j - 1) * (bytewidth) + i * 3 + 2 + 54];
                                        }
                                     }
                                    pR1 = Marshal.UnsafeAddrOfPinnedArrayElement(imagedataR, 0);
                                    pG1 = Marshal.UnsafeAddrOfPinnedArrayElement(imagedataG, 0);
                                    pB1 = Marshal.UnsafeAddrOfPinnedArrayElement(imagedataB, 0);
                                    HOperatorSet.GenImage3(out image, "byte", stFrameInfo.nWidth, stFrameInfo.nHeight, pR1, pG1, pB1);

                                  break;
                              case CameraColorEnum.R:   

                                     for (int j = 0; j < stFrameInfo.nHeight; j++)
                                      {
                                          for (int i = 0; i < stFrameInfo.nWidth; i++)
                                           {imagedataR[j * bytewidthg + i] = m_pBufForSaveImage[(stFrameInfo.nHeight - j - 1) * (bytewidth) + i * 3 + 2 + 54];}
                                       }
                                       pR1 = Marshal.UnsafeAddrOfPinnedArrayElement(imagedataR, 0);
                                       HOperatorSet.GenImage1(out image, "byte", stFrameInfo.nWidth, stFrameInfo.nHeight, pR1);
                                  break;
                              case CameraColorEnum.G:

                                      for (int j = 0; j < stFrameInfo.nHeight; j++)
                                        {
                                          for (int i = 0; i < stFrameInfo.nWidth; i++)
                                           { imagedataG[j * bytewidthg + i] = m_pBufForSaveImage[(stFrameInfo.nHeight - j - 1) * (bytewidth) + i * 3 + 1 + 54];}
                                       }
                                       pG1 = Marshal.UnsafeAddrOfPinnedArrayElement(imagedataG, 0);
                                       HOperatorSet.GenImage1(out image, "byte", stFrameInfo.nWidth, stFrameInfo.nHeight, pG1);  

                                  break;
                              case CameraColorEnum.B:

                                      for (int j = 0; j < stFrameInfo.nHeight; j++)
                                        {
                                          for (int i = 0; i < stFrameInfo.nWidth; i++)
                                           {imagedataB[j * bytewidthg + i] = m_pBufForSaveImage[(stFrameInfo.nHeight - j - 1) * (bytewidth) + i * 3 + 54];}
                                       }
                                       pB1 = Marshal.UnsafeAddrOfPinnedArrayElement(imagedataB, 0);
                                       HOperatorSet.GenImage1(out image, "byte", stFrameInfo.nWidth, stFrameInfo.nHeight, pB1);  
                                     

                                  break;
                          }
                          objCameraHalcon.Image1 = image;
                          return image;
                      }
                      catch (Exception ex)
                      {
                          LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, objCameraHalcon.CameraName + "取像异常!" +ex.Message));
                          return image;
                      }

                  }
                  else {  return image; }

              }

              
              /// <summary>
              /// 软件触发一次，通常和方法SaveImage配套使用
              /// </summary>
              /// <returns></returns>
              public bool SoftWare()  //软件触发
              {
                  if (CameraOperator.CO_OK != m_pOperator1.CommandExecute("TriggerSoftware"))
                  {
                      m_bGrabbing = false; return false;
                  }
                  else { return true; }
              }



              /// <summary>
              /// 停止采集
              /// </summary>
              public void StopGrap(CameraHalcon objCameraHalcon)
              {
                  
                  m_pOperator1.StopGrabbing();
                  m_bGrabbing = false;
                  //m_bOpen = false;
                  LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "已关闭采集!"));
              }
              

              /// <summary>
              /// 关闭相机
              /// </summary>
              public void closeCamera(CameraHalcon objCameraHalcon)
              {
                 
                  if (m_bGrabbing != false)
                  { StopGrap(objCameraHalcon); }
                  m_pOperator1.Close();
                  m_bGrabbing = false;
                  m_bOpen = false; 
                  LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.White, objCameraHalcon.CameraName + "相机已关闭!"));
              }


              /// <summary>
              /// 保存原图/根据时间自动创建时间子文件夹/图片格式为Jpg/文件名前会自动加上 hh-mm-ss  
              /// </summary>
              /// <param name="image">图片/格式为Halcon的hobject</param>
              /// <param name="file">文件夹地址</param>
              /// <param name="imagename">文件名，会自动在这之前添加time（hh-mm-ss）</param>
              public  void ALLImageJpg(HObject image, string file, string imagename)
              {
                  try
                  {
                      string time = DateTime.Now.ToString("MM_dd");
                      string filename = file + time + "\\";
                      //如果没有就创建新的
                      if (!Directory.Exists(filename))
                      {
                          Directory.CreateDirectory(filename);
                      }
                      HOperatorSet.WriteImage(image, "jpg", 0, filename + DateTime.Now.ToString("HH_mm_ss") + "_" + imagename + ".jpg");
                  }
                  catch (Exception ex)
                  {
                      m_bOpen = false; LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, "方法ALLImageJpg保存图像异常" + file + imagename + "\r\n" + ex.Message));
                      //LogHelper.WriteLog("方法ALLImageJpg保存图像异常" + file + imagename + "\r\n" + ex.Message);
                  }
              }


              /// <summary>
              /// 保存现有工站图片//不会自动依照时间创建子文件夹
              /// </summary>
              /// <param name="image">图片/格式为Halcon的hobject</param>
              /// <param name="file">文件夹地址</param>
              /// <param name="imagename">文件名</param>
              public  void NowImageBMP(HObject image, string file, string imagename)
              {
                  try
                  {
                      //如果没有就创建新的
                      if (!Directory.Exists(file))
                      {
                          Directory.CreateDirectory(file);
                      }
                      HOperatorSet.WriteImage(image, "bmp", 0, file + imagename + ".bmp");
                  }
                  catch (Exception ex)
                  {
                      m_bOpen = false; LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, "方法NowImageBMP保存图像异常" + file + imagename + "\r\n" + ex.Message));
                      //LogHelper.WriteLog("方法NowImageBMP保存图像异常" + file + imagename + "\r\n" + ex.Message);
                  }
              }


              /// <summary>
              /// 根据地址读取图片，返回null意味失败，异常写入日志
              /// </summary>
              /// <param name="path"></param>
              /// <returns></returns>
              public  HObject ReadImage(string path,CameraHalcon objcamera)
              {
                  HObject image = null;
                  if (!File.Exists(path))
                  {
                      LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, "图片地址不存在" + path));
                      //LogHelper.WriteLog("类FileImageHalcon的方法，读取的文件不存在");
                      return image;
                  }
                  else
                  {
                      try
                      {
                          HOperatorSet.ReadImage(out image, new HTuple(path));
                          objcamera.Image1 = image;
                          return image;
                      }
                      catch (Exception ex)
                      {
                          LogDisplayEventProgress(new Log(DateTime.Now.ToString("G"), Color.Red, "方法ReadImage异常：" + path + "   " + ex.Message));
                          //LogHelper.WriteLog("类FileImageHalcon的方法ReadImage异常：" + path + "   " + ex.Message);
                          return image;
                      }
                  }
              }


              /// <summary>
              /// 对类cameraHalcon单个类对象进行写入ini文件
              /// </summary>
              /// <param name="objcamera">cameraHalconcase类对象</param>
              /// <param path="path">文件名全称地址</param>
              /// <param name="name">文件名,不用加后缀，方法中会带/</param>
              /// <returns></returns>
              public virtual bool Write(CameraHalcon objcamera, string filepath, string name)
              {
                  //string path = filepath + name;
                  //return CameraHalconDataServers.WriteBin(objcamera, path);
                  return false;
              }


              /// <summary>
              /// 根据二进制ini文件来进行读取，对类cameraHalcon单个类对象进行读取
              /// </summary>
              /// <param name="filepath">文件所在目录</param>
              /// <param name="name">文件名，不用加后缀，方法中会带</param>
              /// <returns></returns>
              public virtual CameraHalcon Read(string filepath, string name)
              {
                  //string path = filepath + name;
                  //return CameraHalconDataServers.ReadBin(path);
                  return null;
              }



              public void DisplayImage(CameraHalcon objcamera,IntPtr id )
              {
                  HOperatorSet.DispObj(objcamera.Image1, id);
              }

              ///// <summary>
              ///// 根据list《相机类》来进行写入二进制ini文件，List的表示多个相机，可以将多个相机集合在一个文件
              ///// </summary>
              ///// <param name="objcameracaseList">写入的LIst类对象</param>
              ///// <param name="filepath">文件所在目录</param>
              ///// <param name="name">文件名,不用加后缀，方法中会带</param>
              ///// <returns></returns>
              //public virtual bool WriteList(List<CameraHalcon> objcameraList, string filepath, string name)
              //{
              //    string path = filepath + name;
              //    return CameraHalconDataServers.WriteListBin(objcameraList, path) && CameraHalconDataServers.WritelistXML(objcameraList, path);

              //}

              ///// <summary>
              ///// 读取Ini文件，获取相机属性类camerhalcon的list集合，返回list集合
              /////
              ///// </summary>
              ///// <param name="filepath">文件所在目录</param>
              ///// <param name="name">文件名，不用加后缀，</param>
              ///// <returns></returns>
              //public virtual List<CameraHalcon> ReadList(string filepath, string name)
              //{
              //    string path = filepath + name;
              //    return CameraHalconDataServers.ReadListBin(path);

              //}

        #endregion

        #region 【 相 机 运 行 】
              
              /// <summary>
              ///  相机的九点标定_运行 //将最后得到的比例系数写入对象objcamera
              /// </summary>
              /// <param name="objcamera">相机对象</param>
              /// <param name="objcross">运动轨迹标记</param>
              /// <returns></returns>
              public virtual bool  Run_9Piont(CameraHalcon objcamera, double juli ,List<double> objlist,out HObject objcross)
              {
                 objcross=null;
                 return false;
              }

            
              /// <summary>
              /// 相机的旋转中心标定_运行  //将最后得到的旋转中心写入对象objcamera
              /// </summary>
              /// <param name="objcamera">相机对象</param>
              /// <param name="objcross">运动轨迹标记</param>
              /// <returns></returns>
              public virtual bool Run_9Rotate(CameraHalcon objcamera, double jiaodu, out HObject objcross)
              {
                  objcross = null;
                  return false;
              }


              /// <summary>
              /// 相机的基准坐标_运行  //将最后得到的基准写入对象objcamera
              /// </summary>
              /// <param name="objcamera">相机对象</param>
              /// <returns></returns>
              public virtual bool Run_Datum(CameraHalcon objcamera, List<double> objlist, out HObject objcross)
              {
                  objcross = null;
                  return false;
              }

              /// <summary>
              /// 相机的图像处理1
              /// </summary>
              /// <param name="objcamera">相机对象</param>
              /// <param name="objvariant">结果放入，变量的对象中</param>
              /// <param name="image">要处理的图像</param>
              /// <returns></returns>
              public virtual bool Run_DealImage1(CameraHalcon objcamera,CameraVariant objvariant,HObject image)
              {

                  return false;
              }


              /// <summary>
              /// 相机的图像处理2
              /// </summary>
              /// <param name="objcamera">相机对象</param>
              /// <param name="objvariant">结果放入，变量的对象中</param>
              /// <param name="image">要处理的图像</param>
              /// <returns></returns>
              public virtual bool Run_DealImage2(CameraHalcon objcamera, CameraVariant objvariant, HObject image)
              {

                  return false;
              }
              /// <summary>
              ///  根据起点生成9点坐标,计算九点矩阵
              /// </summary>
              /// <param name="juli">每次移动间距</param>
              /// <param name="objlist"></param>
              /// <returns></returns>
              public virtual bool Create_piont1(double  juli, List<string> objlist)
              {
                  if (objlist.Count >= 1)
                  {
                      string[] temp = objlist[0].Split(',');
                      objlist.Clear();
                      double[] juli_x = { 0, juli, juli, 0, (-juli), (-juli), (-juli), 0, juli };
                      double[] juli_y = { 0, 0, juli, juli, juli, 0, (-juli), (-juli), (-juli) };
                      for (int i = 0; i < 9; i++)
                      { 
                          StringBuilder temp1 = new StringBuilder();
                          temp1.Append("PH");
                          temp1.Append(",");
                          temp1.Append((Convert.ToDouble(temp[1]) + juli_x[i]).ToString());
                          temp1.Append(",");
                          temp1.Append((Convert.ToDouble(temp[2]) + juli_y[i]).ToString());
                          temp1.Append(",");
                          temp1.Append(temp[3]);
                          temp1.Append(",");
                          temp1.Append(temp[4]);
                          temp1.Append(",");
                          temp1.Append(temp[5]);
                          temp1.Append(",");
                          temp1.Append(temp[6]);
                          objlist.Add(temp1.ToString());
                      }
                      return true; 
                  }
                  else { return false; }
                 
              }

              /// <summary>
              /// 根据起点生成9点旋转坐标，计算旋转中心
              /// </summary>
              /// <param name="objcamera">相机对象</param>
              /// <param name="objstring">返回的9点坐标数组</param>
              /// <returns></returns>
              public virtual bool Create_piont2(double jiaodu, int num, List<string> objlist)
              {
                  if (objlist.Count >= 1)
                  {
                      string[] temp = objlist[0].Split(',');
                      for (int i = 0; i<= num; i++)
                      {
                          StringBuilder temp1 = new StringBuilder();
                          temp1.Append("PH");
                          temp1.Append(",");
                          temp1.Append(temp[1]);
                          temp1.Append(",");
                          temp1.Append(temp[2]);
                          temp1.Append(",");
                          temp1.Append(temp[3]);
                          temp1.Append(",");
                          temp1.Append(temp[4]);
                          temp1.Append(",");
                          temp1.Append(temp[5]);
                          temp1.Append(",");
                          temp1.Append((Convert.ToDouble( temp[6]) + jiaodu*i).ToString());
                          objlist.Add(temp1.ToString());
                      }
                      return true;
                  }
                  else { return false; }
              }

        #endregion
    }
}



﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HalconDotNet;
using MvCamCtrl.NET;
using System.Runtime.InteropServices;
using System.Threading;
using System.IO;
using System.Security.Cryptography;
using Dapper;
using CamprocessModel;
using ReadFog.Lib;
using OpenCvSharp;
using 视觉检测系统;

namespace 磨边机视觉定位系统
{
    
    public partial class Form1 : Form
    {
        MyCamera.MV_CC_DEVICE_INFO_LIST m_pDeviceList;
        public MyCamera m_pMyCamera1;
        public MyCamera m_pMyCamera2;
        bool m_bGrabbing1;
        bool m_bGrabbing2;
        public Image_Zoom IZHw = new Image_Zoom();
        public Process Pcs = new Process();
        byte[] m_pBufForSaveImage1 = new byte[50 * 1024 * 1024];
        byte[] m_pBufForSaveImage2 = new byte[50 * 1024 * 1024];
        public HObject Image1=new HObject();
        public  HObject Image2 = new HObject();
        public Function FcnActiong = new Function();
        public int CameraOneAction = 0;
        public int  CameraTwoAction = 0;
       
        public string S1 = "GDWBZNKJ";
        public string S2 = "KSWBZNKJ";
        public Computer dt = Computer.Instance();

        List<int> result1 = new List<int>();//0为初始状态，1表示OK，2表示NG，
        List<int> result2 = new List<int>();//0为初始状态，1表示OK，2表示NG，

        object oj1 = new object();
        object oj2 = new object();

        Thread ThreadRstIO;
        public Mat cMat;
        public Form1()
        {
            InitializeComponent();
            m_pDeviceList = new MyCamera.MV_CC_DEVICE_INFO_LIST();
            m_pMyCamera1 = new MyCamera();
            m_pMyCamera2 = new MyCamera();
            m_bGrabbing1 = false;
            m_bGrabbing2 = false;
            Pcs.FilePath=Application.StartupPath +"\\config\\Fileconfig.ini";
            Pcs.FileRecordPath = Application.StartupPath + "\\config\\Key.ini";
         
            Pcs.CalNumLeft = 0;
            Pcs.CalNumRight = 0;

            Pcs.ProductCode = string.Empty;
            Pcs.IsChoseProduct = false;
            Pcs.SuccToCnctPlc = false;
            Pcs.PowerState = false;
            Pcs.IsLoadCamProcess = false;

            Pcs.BnThread = true;
            Pcs.IsStartSoft = false;
            Pcs.Level = 0;
            Pcs.JugleAngle = 0.0;
        }
        private bool isOpen = false;//相机打开标志位
        private void DeviceListAcq()
        {
            int nRet;
            // ch:创建设备列表 || en: Create device list
            System.GC.Collect();
            //cbDeviceList.Items.Clear();
            nRet = MyCamera.MV_CC_EnumDevices_NET(MyCamera.MV_GIGE_DEVICE | MyCamera.MV_USB_DEVICE, ref m_pDeviceList);
            if (0 != nRet)
            {
                MessageBox.Show("Enum Devices Fail");
                return;
            }

            // ch:在窗体列表中显示设备名 || Display the device'name on window's list
            for (int i = 0; i < m_pDeviceList.nDeviceNum; i++)
            {
                MyCamera.MV_CC_DEVICE_INFO device = (MyCamera.MV_CC_DEVICE_INFO)Marshal.PtrToStructure(m_pDeviceList.pDeviceInfo[i], typeof(MyCamera.MV_CC_DEVICE_INFO));

                if (device.nTLayerType == MyCamera.MV_GIGE_DEVICE)
                {
                    IntPtr buffer = Marshal.UnsafeAddrOfPinnedArrayElement(device.SpecialInfo.stGigEInfo, 0);
                    MyCamera.MV_GIGE_DEVICE_INFO gigeInfo = (MyCamera.MV_GIGE_DEVICE_INFO)Marshal.PtrToStructure(buffer, typeof(MyCamera.MV_GIGE_DEVICE_INFO));

                    if ((gigeInfo.chManufacturerName.ToString() + ":" + gigeInfo.chSerialNumber.ToString()) == Pcs.CamNm1)
                    {
                        nRet = m_pMyCamera1.MV_CC_CreateDevice_NET(ref device);
                        if (MyCamera.MV_OK != nRet)
                        {
                            return;
                        }

                        // ch:打开设备 | en:Open device
                        nRet = m_pMyCamera1.MV_CC_OpenDevice_NET();
                        if (MyCamera.MV_OK != nRet)
                        {
                            ShowMsg("相机1打开失败");
                            return;
                        }
                        m_pMyCamera1.MV_CC_SetHeartBeatTimeout_NET(3000);
                        // ch:设置触发模式为off || en:set trigger mode as off
                        m_pMyCamera1.MV_CC_SetEnumValue_NET("AcquisitionMode", 2);
                        m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerMode", 1);

                        m_pMyCamera1.MV_CC_SetEnumValue_NET("LineSelector", 1);
                        m_pMyCamera1.MV_CC_SetEnumValue_NET("LineMode", 8);
                        m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerSource", 0);
                        // m_pMyCamera.MV_CC_SetEnumValue_NET("StrobeLineDuration", 0);
                        // m_pMyCamera.MV_CC_SetEnumValue_NET("StrobeLineDelay", 0);
                        // m_pMyCamera.MV_CC_SetEnumValue_NET("StrobeLinePreDelay", 0);
                        m_pMyCamera1.MV_CC_SetBoolValue_NET("StrobeEnable", true);
                        //m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");

                       
                       
                        /**********************************************************************************************************/
                        // ch:注册回调函数 | en:Register image callback
                        ImageCallback1 = new MyCamera.cbOutputExdelegate(GrabImage1);

                        nRet = m_pMyCamera1.MV_CC_RegisterImageCallBackForRGB_NET(ImageCallback1, IntPtr.Zero);
                        if (MyCamera.MV_OK != nRet)
                        {
                            ShowMsg("工位1相机连接失败>>>");
                            
                        }
                        else
                        {
                            toolStripStatusLabel1.Text = "相机ID：" + gigeInfo.chManufacturerName.ToString() + ":" + gigeInfo.chSerialNumber.ToString();
                            //comboBox1.Items.Add(gigeInfo.chModelName.ToString());
                            ShowMsg("工位1相机连接成功>>>");
                            isOpen = true;
                        }
                        m_pMyCamera1.MV_CC_StartGrabbing_NET();
                        /**********************************************************************************************************/
                        // SetCtrlWhenOpen();
                        try
                        {
                            MyCamera.MVCC_INTVALUE hImageHeight = new MyCamera.MVCC_INTVALUE();
                            MyCamera.MVCC_INTVALUE hImageWidth = new MyCamera.MVCC_INTVALUE();
                            m_pMyCamera1.MV_CC_GetHeight_NET(ref hImageHeight);
                            m_pMyCamera1.MV_CC_GetWidth_NET(ref hImageWidth);
                            string exposureTime = iniFileUtil.ReadString("param", "exposureTime", "");
                            string gain = iniFileUtil.ReadString("param", "gain", "");
                            exposureTxt.Text = exposureTime;
                            gainTxt.Text = gain;
                            if (!string.IsNullOrEmpty(exposureTime))
                            {
                                m_pMyCamera1.MV_CC_SetEnumValue_NET("ExposureAuto", 0);
                                m_pMyCamera1.MV_CC_SetFloatValue_NET("ExposureTime", float.Parse(exposureTime));
                            }
                            if (!string.IsNullOrEmpty(gain))
                            {
                                m_pMyCamera1.MV_CC_SetEnumValue_NET("GainAuto", 0);
                                nRet = m_pMyCamera1.MV_CC_SetFloatValue_NET("Gain", float.Parse(gain));
                            }
                            HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hImageHeight.nCurValue, hImageWidth.nCurValue);// ch: 使图像显示适应窗口大小 || en: Make the image adapt the window size
                        }
                        catch (System.Exception ex)
                        {
                            MessageBox.Show(ex.ToString());
                            return;
                        }
                       
                    }
                    //else if ((gigeInfo.chManufacturerName.ToString() + ":" + gigeInfo.chSerialNumber.ToString()) == Pcs.CamNm2)
                    //{
                    //    nRet = m_pMyCamera2.MV_CC_CreateDevice_NET(ref device);
                    //    if (MyCamera.MV_OK != nRet)
                    //    {
                    //        return;
                    //    }

                    //    // ch:打开设备 | en:Open device
                    //    nRet = m_pMyCamera2.MV_CC_OpenDevice_NET();
                    //    if (MyCamera.MV_OK != nRet)
                    //    {
                    //        ShowMsg("相机2打开失败");
                    //        return;
                    //    }
                    //    m_pMyCamera2.MV_CC_SetHeartBeatTimeout_NET(3000);
                    //    // ch:设置触发模式为off || en:set trigger mode as off
                    //    m_pMyCamera2.MV_CC_SetEnumValue_NET("AcquisitionMode", 2);
                    //    m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerMode", 1);
                    //    m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerSource", 0);
                    //    m_pMyCamera2.MV_CC_SetEnumValue_NET("LineSelector", 1);
                    //    m_pMyCamera2.MV_CC_SetEnumValue_NET("LineMode", 8);

                    //    // m_pMyCamera.MV_CC_SetEnumValue_NET("StrobeLineDuration", 0);
                    //    // m_pMyCamera.MV_CC_SetEnumValue_NET("StrobeLineDelay", 0);
                    //    // m_pMyCamera.MV_CC_SetEnumValue_NET("StrobeLinePreDelay", 0);
                    //    m_pMyCamera2.MV_CC_SetBoolValue_NET("StrobeEnable", true);
                    //    //m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
                        
                    //    /**********************************************************************************************************/
                    //    // ch:注册回调函数 | en:Register image callback
                    //    ImageCallback2 = new MyCamera.cbOutputExdelegate(GrabImage2);

                    //    nRet = m_pMyCamera2.MV_CC_RegisterImageCallBackForRGB_NET(ImageCallback2, IntPtr.Zero);
                    //    if (MyCamera.MV_OK != nRet)
                    //    {
                    //        ShowMsg("工位2相机连接失败>>>");
                    //        //MessageBox.Show("Register Image CallBack Fail");
                    //    }
                    //    else
                    //    {
                    //        toolStripStatusLabel3.Text = "相机ID：" + gigeInfo.chManufacturerName.ToString() + ":" + gigeInfo.chSerialNumber.ToString();
                    //        //comboBox1.Items.Add(gigeInfo.chModelName.ToString());
                    //        ShowMsg("工位2相机连接成功>>>");
                    //    }
                    //    m_pMyCamera2.MV_CC_StartGrabbing_NET();
                    //    /**********************************************************************************************************/
                    //    //SetCtrlWhenOpen();
                    //    try
                    //    {
                    //        MyCamera.MVCC_INTVALUE hImageHeight = new MyCamera.MVCC_INTVALUE();
                    //        MyCamera.MVCC_INTVALUE hImageWidth = new MyCamera.MVCC_INTVALUE();
                    //        m_pMyCamera2.MV_CC_GetHeight_NET(ref hImageHeight);
                    //        m_pMyCamera2.MV_CC_GetWidth_NET(ref hImageWidth);

                    //        HOperatorSet.SetPart(hWindowControl2.HalconWindow, 0, 0, hImageHeight.nCurValue, hImageWidth.nCurValue);// ch: 使图像显示适应窗口大小 || en: Make the image adapt the window size
                    //    }
                    //    catch (System.Exception ex)
                    //    {
                    //        MessageBox.Show(ex.ToString());
                    //        return;
                    //    }
                       
                    //}

                }
                else if (device.nTLayerType == MyCamera.MV_USB_DEVICE)
                {
                    IntPtr buffer = Marshal.UnsafeAddrOfPinnedArrayElement(device.SpecialInfo.stUsb3VInfo, 0);
                    MyCamera.MV_USB3_DEVICE_INFO usbInfo = (MyCamera.MV_USB3_DEVICE_INFO)Marshal.PtrToStructure(buffer, typeof(MyCamera.MV_USB3_DEVICE_INFO));
                    if (usbInfo.chUserDefinedName != "")
                    {
                        //  cbDeviceList.Items.Add("USB: " + usbInfo.chUserDefinedName + " (" + usbInfo.chSerialNumber + ")");
                    }
                    else
                    {
                        //  cbDeviceList.Items.Add("USB: " + usbInfo.chManufacturerName + " " + usbInfo.chModelName + " (" + usbInfo.chSerialNumber + ")");
                    }
                }
                
        }          
            //.ch: 选择第一项 || en: Select the first item
            if (m_pDeviceList.nDeviceNum != 0)
            {
                //cbDeviceList.SelectedIndex = 0;
            }
        }
        /// <summary>
        ///空间按照窗口变化而变化
        /// </summary>
        private float X;        private float Y;
        private void setTag(Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                con.Tag = con.Width + ":" + con.Height + ":" + con.Left + ":" + con.Top + ":" + con.Font.Size;
                if (con.Controls.Count > 0)
                    setTag(con);
            }
        }
        private void setControls(float newx, float newy, Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                string[] mytag = con.Tag.ToString().Split(new char[] { ':' });
                float a = Convert.ToSingle(mytag[0]) * newx;
                con.Width = (int)a;
                a = Convert.ToSingle(mytag[1]) * newy;
                con.Height = (int)a;
                a = Convert.ToSingle(mytag[2]) * newx;
                con.Left = (int)a;
                a = Convert.ToSingle(mytag[3]) * newy;
                con.Top = (int)a;
                Single currentSize = Convert.ToSingle(mytag[4]) * Math.Min(newx, newy);
                con.Font = new Font(con.Font.Name, currentSize, con.Font.Style, con.Font.Unit);
                if (con.Controls.Count > 0)
                {
                    setControls(newx, newy, con);
                }
            }

        }
        void Form1_Resize(object sender, EventArgs e)
        {
            float newx = (this.Width) / X;
            float newy = this.Height / Y;
            setControls(newx, newy, this);
            //this.Text = this.Width.ToString() + " " + this.Height.ToString();

        }
        MyCamera.cbOutputExdelegate ImageCallback1;
        MyCamera.cbOutputExdelegate ImageCallback2;
        public bool ChargeHardware()
        {
            bool m = false;
            string s1 = dt.GetCpuID().Trim();
            string s2 = dt.GetDiskID().Trim();
            string[] sc = s1.Split(' ');
            string[] st = s2.Split(' ');
            string sm1 = string.Empty;
            string sm2 = string.Empty;
            for (int j = 0; j < sc.Length; j++)
            {
                sm1 += sc[j];
            }
            for (int p = 0; p < st.Length; p++)
            {
                sm2 += st[p];
            }

            string s = sm1 + sm2;
            string SS = DESEncrypt(s, S2, S1);

            string StrRecord = ConfigIni.GetIniKeyValue("密钥", "密码", "0123456789", Pcs.FileRecordPath);
            if (StrRecord != SS)
            {
                m = false;
            }
            else
            {
                m = true;
            }
            return m;
        }
        /// <summary>
        /// DES加密
        /// </summary>
        /// <param name="data">加密数据</param>
        /// <param name="key">8位字符的密钥字符串</param>
        /// <param name="iv">8位字符的初始化向量字符串</param>
        /// <returns></returns>
        public static string DESEncrypt(string data, string key, string iv)
        {
            byte[] byKey = System.Text.ASCIIEncoding.ASCII.GetBytes(key);
            byte[] byIV = System.Text.ASCIIEncoding.ASCII.GetBytes(iv);

            DESCryptoServiceProvider cryptoProvider = new DESCryptoServiceProvider();
            int i = cryptoProvider.KeySize;
            MemoryStream ms = new MemoryStream();
            CryptoStream cst = new CryptoStream(ms, cryptoProvider.CreateEncryptor(byKey, byIV), CryptoStreamMode.Write);

            StreamWriter sw = new StreamWriter(cst);
            sw.Write(data);
            sw.Flush();
            cst.FlushFinalBlock();
            sw.Flush();
            return Convert.ToBase64String(ms.GetBuffer(), 0, (int)ms.Length);
        }
        /// <summary>
        /// DES解密
        /// </summary>
        /// <param name="data">解密数据</param>
        /// <param name="key">8位字符的密钥字符串(需要和加密时相同)</param>
        /// <param name="iv">8位字符的初始化向量字符串(需要和加密时相同)</param>
        /// <returns></returns>
        public static string DESDecrypt(string data, string key, string iv)
        {
            byte[] byKey = System.Text.ASCIIEncoding.ASCII.GetBytes(key);
            byte[] byIV = System.Text.ASCIIEncoding.ASCII.GetBytes(iv);

            byte[] byEnc;
            try
            {
                byEnc = Convert.FromBase64String(data);
            }
            catch
            {
                return null;
            }

            DESCryptoServiceProvider cryptoProvider = new DESCryptoServiceProvider();
            MemoryStream ms = new MemoryStream(byEnc);
            CryptoStream cst = new CryptoStream(ms, cryptoProvider.CreateDecryptor(byKey, byIV), CryptoStreamMode.Read);
            StreamReader sr = new StreamReader(cst);
            return sr.ReadToEnd();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            //if (ChargeHardware() == false)
            //{
            //    MessageBox.Show("请注册密钥");
            //    this.Close();
            //    return;
            //}
            UpForm();
            HOperatorSet.SetCheck("~give_error");
            HOperatorSet.SetSystem("clip_region", "false");

            this.splitContainer2.SplitterDistance = this.splitContainer2.Width / 2;
            //窗口变化
            this.Resize += new EventHandler(Form1_Resize);
            X = this.Width;
            Y = this.Height;
            setTag(this);
            Form1_Resize(new object(), new EventArgs());

            GetFile();

            Thread.Sleep(100);
           // DisplayWindowsInitial();
            DeviceListAcq();
            
            Thread.Sleep(200);
            
            
            List<string> productcode= GetProductList();
            if (productcode.Count() > 0)
            {
                for (int k = 0; k < productcode.Count(); k++)
                {
                    comboBox3.Items.Add(productcode[k]);
                }
            }

        }

        void ThreadAction()
        {
            while (Pcs.BnThread)
            {
                Application.DoEvents();
                if (result1.Count > 0 && result2.Count > 0)
                {
                    if (result1[0] == 1 && result2[0] == 1)
                    {
                        m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
                    }
                    else
                    {
                        m_pMyCamera2.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
                    }

                    lock(oj1)
                    {
                        lock(oj2)
                        {
                            result1.RemoveAt(0);
                            result2.RemoveAt(0);
                        }
                    }
                }
                Thread.Sleep(150);
            }
        }
        List<string> GetProductList()
        {
            var sql = @"SELECT productcode from  processname ORDER BY id";
            using (var con = DbHelper.GetDbConnection())
            {               
                var ps = new DynamicParameters();
                processname model = new processname();
                var res = con.Query<string>(sql, ps).ToList(); 
                return res;
               
            }

        }




        void ShowMsg(string str)
        {
            DateTime dt = new DateTime();
            dt = DateTime.Now;
            
            this.Invoke((EventHandler)(delegate
            {
                textBox1.AppendText(dt.ToString("yyyy/MM/dd hh:mm:ss") + str + "\r\n");
            }));

        }

        bool GetCamProcess()
        {
            var res = SelectCamLeftData(Pcs.ProductCode);

            if (res.Items.Count() > 0)
            {
                Pcs.m_ErModelParam.length1 = Convert.ToDouble(res.Items[0].length1);
                Pcs.m_ErModelParam.length2 = Convert.ToDouble(res.Items[0].length2);
                

                HOperatorSet.ReadRegion(out Pcs.m_ErModelParam.hmregion, Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg");
                
                txtlength1.Text = Pcs.m_ErModelParam.length1.ToString();
                txtlength2.Text = Pcs.m_ErModelParam.length2.ToString();
                

                ShowMsg("左相机数据参数加载成功");
            }
            else
            {
                MessageBox.Show("左相机数据查询失败", "请检查数据完整性");
                return false;
            }
            
            comboBox10.SelectedIndex = 0;

            var ves = SelectCamRightData(Pcs.ProductCode);
            if (ves.Items.Count() > 0)
            {
                Pcs.Holeparammodel.score = ves.Items[0].score;
                Pcs.Holeparammodel.numlevel = ves.Items[0].numlevel;
                Pcs.Holeparammodel.minangle = ves.Items[0].minangle;
                Pcs.Holeparammodel.maxangle = ves.Items[0].maxangle;
                Pcs.Holeparammodel.angelstep = ves.Items[0].angelstep;
                Pcs.Holeparammodel.mincontrast = ves.Items[0].mincontrast;
                Pcs.Holeparammodel.highcontrast = ves.Items[0].highcontrast;
                Pcs.Holeparammodel.lowcontrast = ves.Items[0].lowcontrast;
                Pcs.Holeparammodel.minsize = ves.Items[0].minsize;

                Pcs.Holeparammodel.Modelparam.Cmx = ves.Items[0].Cmx;
                Pcs.Holeparammodel.Modelparam.Cmy = ves.Items[0].Cmy;
                Pcs.Holeparammodel.Modelparam.Cma = ves.Items[0].Cma;

                txb_score.Text = Pcs.Holeparammodel.score.ToString();
                txb_numlevel.Text = Pcs.Holeparammodel.numlevel.ToString();
                txb_minangle.Text = Pcs.Holeparammodel.minangle.ToString();
                txb_maxangle.Text = Pcs.Holeparammodel.maxangle.ToString();
                txb_anglestep.Text = Pcs.Holeparammodel.angelstep.ToString();
                txb_mincontrast.Text = Pcs.Holeparammodel.mincontrast.ToString();
                txb_lowcontrast.Text = Pcs.Holeparammodel.highcontrast.ToString();
                txb_highcontrast.Text = Pcs.Holeparammodel.lowcontrast.ToString();
                txb_minsize.Text = Pcs.Holeparammodel.minsize.ToString();

                txb_modelx.Text = Pcs.Holeparammodel.Modelparam.Cmx.ToString();
                txb_modely.Text = Pcs.Holeparammodel.Modelparam.Cmy.ToString();
                txb_modela.Text = Pcs.Holeparammodel.Modelparam.Cma.ToString();

                HOperatorSet.ReadRegion(out Pcs.Holeparammodel.Modelparam.Cregion, Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg");
                HOperatorSet.ReadShapeModel(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".shm", out Pcs.Holeparammodel.Modelparam.ModelId);

                comboBox10.SelectedIndex = 0;

                Pcs.Holeparammodel.ampm.r2_1x = ves.Items[0].r2_1x;
                Pcs.Holeparammodel.ampm.r2_1y = ves.Items[0].r2_1y;
                Pcs.Holeparammodel.ampm.r2_1a = ves.Items[0].r2_1a;
                Pcs.Holeparammodel.ampm.r2_1l1 = ves.Items[0].r2_1l1;
                Pcs.Holeparammodel.ampm.r2_1l2 = ves.Items[0].r2_1l2;

                Pcs.Holeparammodel.ampm.r2_2x = ves.Items[0].r2_2x;
                Pcs.Holeparammodel.ampm.r2_2y = ves.Items[0].r2_2y;
                Pcs.Holeparammodel.ampm.r2_2a = ves.Items[0].r2_2a;
                Pcs.Holeparammodel.ampm.r2_2l1 = ves.Items[0].r2_2l1;
                Pcs.Holeparammodel.ampm.r2_2l2 = ves.Items[0].r2_2l2;

                Pcs.Holeparammodel.ampm.x1 = ves.Items[0].x1;
                Pcs.Holeparammodel.ampm.y1 = ves.Items[0].y1;
                Pcs.Holeparammodel.ampm.x2 = ves.Items[0].x2;
                Pcs.Holeparammodel.ampm.y2 = ves.Items[0].y2;

                txb_L1row.Text = Pcs.Holeparammodel.ampm.r2_1x.ToString();
                txb_L1col.Text = Pcs.Holeparammodel.ampm.r2_1y.ToString();
                txb_L1angle.Text = Pcs.Holeparammodel.ampm.r2_1a.ToString();
                txb_L1length1.Text = Pcs.Holeparammodel.ampm.r2_1l1.ToString();
                txb_L1length2.Text = Pcs.Holeparammodel.ampm.r2_1l2.ToString();

                txb_L2row.Text = Pcs.Holeparammodel.ampm.r2_2x.ToString();
                txb_L2col.Text = Pcs.Holeparammodel.ampm.r2_2y.ToString();
                txb_L2angle.Text = Pcs.Holeparammodel.ampm.r2_2a.ToString();
                txb_L2length1.Text = Pcs.Holeparammodel.ampm.r2_2l1.ToString();
                txb_L2length2.Text = Pcs.Holeparammodel.ampm.r2_2l2.ToString();

                txb_mox1.Text = Pcs.Holeparammodel.ampm.x1.ToString();
                txb_moy1.Text = Pcs.Holeparammodel.ampm.y1.ToString();
                txb_mox2.Text = Pcs.Holeparammodel.ampm.x2.ToString();
                txb_moy2.Text = Pcs.Holeparammodel.ampm.y2.ToString();

                comboBox1.SelectedIndex = 0;
                ShowMsg("右相机数据参数加载成功");
            }
            
            else
            {
                MessageBox.Show("右相机数据查询失败", "请检查数据完整性");
                return false;
            }
            return true;
        }
        private void GetFile()
        {
            //ShowMsg("加载工程数据>>>");
            try
            {

                Pcs.IPAdress = ConfigIni.GetIniKeyValue("地址", "IP", "************", Pcs.FilePath);
                textBox22.Text = Pcs.IPAdress;
               
                Pcs.JugleAngle = Convert.ToDouble(ConfigIni.GetIniKeyValue("系统参数", "角度判定", "0.5", Pcs.FilePath));
                textBox4.Text = Pcs.JugleAngle.ToString();

                Pcs.CamNm1 =(ConfigIni.GetIniKeyValue("系统参数", "相机1", "GEV:*********", Pcs.FilePath));
                textBox2.Text = Pcs.CamNm1.ToString();

                Pcs.CamNm2 = (ConfigIni.GetIniKeyValue("系统参数", "相机2", "GEV:*********", Pcs.FilePath));
                textBox3.Text = Pcs.CamNm2.ToString();
            }
            catch (Exception ex)
            {
                ShowMsg("数据加载失败>>>");
            }
        }

        PagedList<camleft> SelectCamLeftData(string _productcode)
        {
            var sql = @"SELECT * from  camleft where 1 = 1 ";//冒号前面加空格";
            
            var ps = new DynamicParameters();

            var where = " AND productcode = @productcode ";
            ps.Add("productcode", _productcode);
            sql = sql + where + " ORDER BY id desc";

            var pageres = new PagedList<camleft>();
            using (var con = DbHelper.GetDbConnection())
            {
                var res = con.QueryMultiple(sql, ps);
                if (!res.IsConsumed)
                {
                    pageres.Items = res.Read<camleft>().ToList();//查询出的数据

                }
            }
            return pageres;
        }

        PagedList<camright> SelectCamRightData(string _productcode)
        {
            var sql = @"SELECT * from  camright where 1 = 1 ";//冒号前面加空格";

            var ps = new DynamicParameters();

            var where = " AND productcode = @productcode ";
            ps.Add("productcode", _productcode);
            sql = sql + where + " ORDER BY id desc";

            var pageres = new PagedList<camright>();
            using (var con = DbHelper.GetDbConnection())
            {
                var res = con.QueryMultiple(sql, ps);
                if (!res.IsConsumed)
                {
                    pageres.Items = res.Read<camright>().ToList();//查询出的数据

                }
            }
            return pageres;
        }
        private void GrabImage2(IntPtr pData, ref MyCamera.MV_FRAME_OUT_INFO_EX pFrameInfo, IntPtr pUser)
        {
            Application.DoEvents();
            HOperatorSet.SetCheck("~give_error");
            HOperatorSet.SetSystem("clip_region", "false");
            if (pData != null)
            {
                uint nWidth = pFrameInfo.nWidth;

                uint nHeight = pFrameInfo.nHeight;

                Marshal.Copy(pData, m_pBufForSaveImage2, 0, (int)nWidth * (int)nHeight * 3);

                
                UInt32 nSupWidth = (pFrameInfo.nWidth + (UInt32)3) & 0xfffffffc;
                Int32 nLength = (Int32)pFrameInfo.nWidth * (Int32)pFrameInfo.nHeight;

                byte[] m_pDataForRed = new byte[(int)nWidth * (int)nHeight];
                byte[] m_pDataForGreen = new byte[(int)nWidth * (int)nHeight];
                byte[] m_pDataForBlue = new byte[(int)nWidth * (int)nHeight];

                for (int nRow = 0; nRow < pFrameInfo.nHeight; nRow++)
                {
                    for (int col = 0; col < pFrameInfo.nWidth; col++)
                    {
                        m_pDataForRed[nRow * nSupWidth + col] = m_pBufForSaveImage2[nRow * pFrameInfo.nWidth * 3 + (3 * col)];
                        m_pDataForGreen[nRow * nSupWidth + col] = m_pBufForSaveImage2[nRow * pFrameInfo.nWidth * 3 + (3 * col + 1)];
                        m_pDataForBlue[nRow * nSupWidth + col] = m_pBufForSaveImage2[nRow * pFrameInfo.nWidth * 3 + (3 * col + 2)];
                    }
                }

                IntPtr RedPtr = BytesToIntptr(m_pDataForRed);
                IntPtr GreenPtr = BytesToIntptr(m_pDataForGreen);
                IntPtr BluePtr = BytesToIntptr(m_pDataForBlue);
                try
                {
                    HOperatorSet.GenEmptyObj(out Image2);
                    Image2.Dispose();
                    HOperatorSet.GenImage3(out Image2, (HTuple)"byte", pFrameInfo.nWidth, pFrameInfo.nHeight,
                                        (new HTuple(RedPtr)), (new HTuple(GreenPtr)), (new HTuple(BluePtr)));
                }
                catch
                {
                    Image2.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                    return;
                }

                // ch: 显示 || display
                HTuple hImageWidth = 0;
                HTuple hImageHeight = 0;
                HTuple point = null;
                HTuple type = null;

                try
                {
                    HOperatorSet.GetImagePointer1(Image2, out point, out type, out hImageWidth, out hImageHeight);//.ch: 得到图像的宽高和指针 || en: Get the width and height of the image
                }
                catch 
                {
                    Image2.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                  
                    return;
                }
                try
                {
                    HOperatorSet.SetPart(hWindowControl2.HalconWindow, 0, 0, hImageHeight, hImageWidth);// ch: 使图像显示适应窗口大小 || en: Make the image adapt the window size
                }
                catch
                {
                    Image2.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                   
                    return;
                }
                    if (hWindowControl2.HalconWindow == null)
                    {
                        return;
                    }
                    try
                    {
                        HOperatorSet.DispObj(Image2, hWindowControl2.HalconWindow);// ch 显示 || en: display

                    if (Pcs.CamRunState == 1)
                    {
                        hWindowControl3.HalconWindow.SetPart(0, 0, hImageHeight, hImageWidth);

                        HOperatorSet.DispObj(Image2, hWindowControl3.HalconWindow);// ch 显示 || en: display
                    }
                    if (Pcs.CamRunState == 2)
                    {
                        hWindowControl3.HalconWindow.SetPart(0, 0, hImageHeight, hImageWidth);

                        HOperatorSet.DispObj(Image2, hWindowControl3.HalconWindow);// ch 显示 || en: display
                    }
                 

                    HOperatorSet.SetSystem("clip_region", "false");
                    HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");
                    HOperatorSet.SetDraw(hWindowControl2.HalconWindow, "margin");
                    HTuple width, height;
                    HOperatorSet.GetImageSize(Image2, out width, out height);
                    HOperatorSet.DispLine(hWindowControl2.HalconWindow, (int)height / 2, 0, (int)height / 2, (int)width);
                    HOperatorSet.DispLine(hWindowControl2.HalconWindow, 0, (int)width / 2, (int)height, (int)width / 2);
                    if (Pcs.CamRunState == 3)
                    {
                        CameraTwoAction = 1;
                        StationTwoAction();
                    }
                    if (Pcs.CamRunState == 0)
                    {
                        Image2.Dispose();
                    }
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                        Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                        Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                    }
               catch 
                {
                    Image2.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                   
                        return;
                    }
                }            
            return;
        }

        public Mat ConvertHObjectToMat(HObject image)
        {
            // 获取图像指针
            HTuple pointerRed = null, pointerGreen = null, pointerBlue = null;
            HTuple type = null, width = null, height = null;
            HOperatorSet.GetImagePointer3(image, out pointerRed, out pointerGreen, out pointerBlue, out type, out width, out height);

            // 获取图像数据
            int imageSize = width * height;
            byte[] dataRed = new byte[imageSize];
            byte[] dataGreen = new byte[imageSize];
            byte[] dataBlue = new byte[imageSize];
            Marshal.Copy(pointerRed, dataRed, 0, imageSize);
            Marshal.Copy(pointerGreen, dataGreen, 0, imageSize);
            Marshal.Copy(pointerBlue, dataBlue, 0, imageSize);

            // 合并通道数据
            byte[] imageData = new byte[imageSize * 3];
            for (int i = 0; i < imageSize; i++)
            {
                imageData[i * 3] = dataBlue[i];     // OpenCV默认是BGR格式
                imageData[i * 3 + 1] = dataGreen[i];
                imageData[i * 3 + 2] = dataRed[i];
            }

            // 创建OpenCvSharp的Mat对象
            Mat cvMat = new Mat(height, width, MatType.CV_8UC3, imageData);

            return cvMat;
        }
        private void GrabImage1(IntPtr pData, ref MyCamera.MV_FRAME_OUT_INFO_EX pFrameInfo, IntPtr pUser)
        {
            Application.DoEvents();
            HOperatorSet.SetCheck("~give_error");
            HOperatorSet.SetSystem("clip_region", "false");
            if (pData != null)
            {
                uint nWidth = pFrameInfo.nWidth;

                uint nHeight = pFrameInfo.nHeight;

                Marshal.Copy(pData, m_pBufForSaveImage1, 0, (int)nWidth * (int)nHeight * 3);

               
                UInt32 nSupWidth = (pFrameInfo.nWidth + (UInt32)3) & 0xfffffffc;
                Int32 nLength = (Int32)pFrameInfo.nWidth * (Int32)pFrameInfo.nHeight;

                byte[] m_pDataForRed = new byte[(int)nWidth * (int)nHeight];
                byte[] m_pDataForGreen = new byte[(int)nWidth * (int)nHeight];
                byte[] m_pDataForBlue = new byte[(int)nWidth * (int)nHeight];
                    
                for (int nRow = 0; nRow < pFrameInfo.nHeight; nRow++)
                {
                    for (int col = 0; col < pFrameInfo.nWidth; col++)
                    {
                        m_pDataForRed[nRow * nSupWidth + col] = m_pBufForSaveImage1[nRow * pFrameInfo.nWidth * 3 + (3 * col)];
                        m_pDataForGreen[nRow * nSupWidth + col] = m_pBufForSaveImage1[nRow * pFrameInfo.nWidth * 3 + (3 * col + 1)];
                        m_pDataForBlue[nRow * nSupWidth + col] = m_pBufForSaveImage1[nRow * pFrameInfo.nWidth * 3 + (3 * col + 2)];
                    }
                }

                IntPtr RedPtr = BytesToIntptr(m_pDataForRed);
                IntPtr GreenPtr = BytesToIntptr(m_pDataForGreen);
                IntPtr BluePtr = BytesToIntptr(m_pDataForBlue);
                try
                {
                    HOperatorSet.GenEmptyObj(out Image1);
                    Image1.Dispose();
                    HOperatorSet.GenImage3(out Image1, (HTuple)"byte", pFrameInfo.nWidth, pFrameInfo.nHeight,
                                        (new HTuple(RedPtr)), (new HTuple(GreenPtr)), (new HTuple(BluePtr)));

                   cMat =  ConvertHObjectToMat(Image1);
                }
                catch
                {
                    Image1.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                   return;
                }

                // ch: 显示 || display
                HTuple hImageWidth = 0;
                HTuple hImageHeight = 0;
                HTuple point = null;
                HTuple type = null;

                try
                {
                    HOperatorSet.GetImagePointer1(Image1, out point, out type, out hImageWidth, out hImageHeight);//.ch: 得到图像的宽高和指针 || en: Get the width and height of the image
                }
                catch 
                {
                    Image1.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                   
                    return;
                }
                try 
                {
                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hImageHeight, hImageWidth);// ch: 使图像显示适应窗口大小 || en: Make the image adapt the window size
                }
                catch
                {
                    Image1.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                   
                    return;
                }
                if (hWindowControl1.HalconWindow == null)
                    {
                        return;
                    }
                    try
                    {
                    
                        HOperatorSet.DispObj(Image1, hWindowControl1.HalconWindow);// ch 显示 || en: display
                    
                    if (Pcs.CamRunState == 1)
                    {
                        hWindowControl3.HalconWindow.SetPart(0, 0, hImageHeight, hImageWidth);

                        HOperatorSet.DispObj(Image1, hWindowControl3.HalconWindow);// ch 显示 || en: display
                    }
                    if (Pcs.CamRunState == 2)
                    {
                        hWindowControl3.HalconWindow.SetPart(0, 0, hImageHeight, hImageWidth);

                        HOperatorSet.DispObj(Image1, hWindowControl3.HalconWindow);// ch 显示 || en: display
                    }
                   
                    HOperatorSet.SetSystem("clip_region", "false");
                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                    HOperatorSet.SetDraw(hWindowControl1.HalconWindow, "margin");
                    HTuple width, height;
                    HOperatorSet.GetImageSize(Image1, out width, out height);
                    HOperatorSet.DispLine(hWindowControl1.HalconWindow, (int)height / 2, 0, (int)height / 2, (int)width);
                    HOperatorSet.DispLine(hWindowControl1.HalconWindow, 0, (int)width / 2, (int)height, (int)width / 2);
                    if (Pcs.CamRunState == 3)
                    {
                        StationOneAction();
                        CameraOneAction = 1;
                    }
                    if (Pcs.CamRunState == 0)
                    {
                        Image1.Dispose();
                    }

                        Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                        Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                        Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                    }
                    catch
                {
                    Image1.Dispose();
                    Marshal.FreeHGlobal(RedPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(GreenPtr);// ch 释放空间 || en: release space
                    Marshal.FreeHGlobal(BluePtr);// ch 释放空间 || en: release space
                  
                        return;
                    }                
            }
            return;
        }
        public static IntPtr BytesToIntptr(byte[] bytes)
        {
            int size = bytes.Length;
            IntPtr buffer = Marshal.AllocHGlobal(size);
            Marshal.Copy(bytes, 0, buffer, size);
            return buffer;
           
        }     

        private void 连续采集ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Pcs.CamRunState = 0;
            if (m_bGrabbing1 != true)
            {
                m_pMyCamera1.MV_CC_StopGrabbing_NET();
            }
            m_bGrabbing1 = true;
            int nRet;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerMode", 0);
            nRet = m_pMyCamera1.MV_CC_StartGrabbing_NET();          
            
        }

        private void 单次采集ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Pcs.CamRunState = 0;
            if (m_bGrabbing1!=true)
            {
                m_pMyCamera1.MV_CC_StopGrabbing_NET();
            }
            m_bGrabbing1 = true;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerSource", 7);
            int nRet;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerMode", 1);
            nRet = m_pMyCamera1.MV_CC_StartGrabbing_NET();
            // ch: 触发命令 || en: Trigger command
            nRet = m_pMyCamera1.MV_CC_SetCommandValue_NET("TriggerSoftware");         
           
        }

        private void 停止采集ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int nRet = -1;
            // ch:停止抓图 || en:Stop grab image
            nRet = m_pMyCamera1.MV_CC_StopGrabbing_NET();
            
            m_bGrabbing1 = false;
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (m_bGrabbing1)
                {
                    m_bGrabbing1 = false;
                    // ch:停止抓图 || en:Stop grab image
                    m_pMyCamera1.MV_CC_StopGrabbing_NET();

                }
                if (m_bGrabbing2)
                {
                    m_bGrabbing2 = false;
                    // ch:关闭设备 || en: Close device
                    m_pMyCamera1.MV_CC_CloseDevice_NET();
                }
                m_bGrabbing2 = false;
                m_bGrabbing1 = false;
                Pcs.BnThread = false;
               
                Thread.Sleep(10);
              
                
            }
            catch
            { }
        }

        private void hWindowControl1_HMouseWheel(object sender, HMouseEventArgs e)
        {
            try
            {
                HTuple mode = e.Delta;
                int button_state;
                double mouse_post_row=0, mouse_pose_col=0;
                HOperatorSet.SetCheck("~give_error");
                HOperatorSet.SetSystem("clip_region", "false");
                hWindowControl1.HalconWindow.GetMpositionSubPix(out mouse_post_row, out mouse_pose_col, out button_state);
                IZHw.DispImageZoom(Image1, hWindowControl1, mode, mouse_post_row, mouse_pose_col);
            }
            catch(Exception ex)
            {

            }
        }
    
        private void 图像还原ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                HTuple hh, hw;
                if (Image1 != null)
                {
                    HOperatorSet.GetImageSize(Image1, out hw, out hh);
                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hh, hw);
                    HOperatorSet.DispObj(Image1, hWindowControl1.HalconWindow);
                }
            }
            catch
            { }
        }
        
        private void buttonX3_Click(object sender, EventArgs e)
        {
            try
            {
                Pcs.m_ErModelParam.length1 = Convert.ToDouble(txtlength1.Text);
                Pcs.m_ErModelParam.length2 = Convert.ToDouble(txtlength2.Text);
                

                camleft cml = new camleft();
                cml.productcode = Pcs.ProductCode;
                cml.length1 = Convert.ToDouble(txtlength1.Text); 
                cml.length2 = Convert.ToDouble(txtlength2.Text);

               int IsUP= UpdataCamLeftProcess(cml);
                if (IsUP == 0)
                {
                    IsUP = WriteCamLeftProcess(cml);                   
                }
                if (IsUP >= 1)
                {
                    HOperatorSet.WriteRegion(Pcs.m_ErModelParam.hmregion, Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg");
                    MessageBox.Show("左相机参数写入成功！");
                }
                else
                {
                    MessageBox.Show("左相机参数写入失败！");
                }
            }
            catch(Exception ex)
            { }
        }

        int UpdataCamLeftProcess(camleft cml)
        {
            var sql = @"UPDATE camleft SET length1 = " + cml.length1.ToString()+ ",length2 = "+cml.length2.ToString() + " WHERE productcode= '" + cml.productcode+"'";
            using (var con = DbHelper.GetDbConnection())
            {
                try
                {
                    var res = con.Execute(sql, cml); //添加成功的话，返回值一定大于0
                    return res;
                }
                catch (Exception ex)
                {
                    return 0;
                }
            }
        }
        int  WriteCamLeftProcess(camleft cml)
        {
            var sql = @"INSERT INTO camleft ( productcode, length1,length2 )
                                        VALUES
	                                (@productcode,
                                     @length1,
                                     @length2)";
            using (var con = DbHelper.GetDbConnection())
            {
                try
                {
                    var res = con.Execute(sql, cml); //添加成功的话，返回值一定大于0
                    return res;
                }
                catch (Exception ex)
                {
                    return 0;
                }
            }


        }

        private void buttonX2_Click(object sender, EventArgs e)
        {
            try
            {
                ErModelParam m_ErModelParam;
                

                m_ErModelParam.length1 = Convert.ToDouble(txtlength1.Text);
                m_ErModelParam.length2 = Convert.ToDouble(txtlength2.Text);
                
                m_ErModelParam.hmregion = Pcs.m_ErModelParam.hmregion;
                
                HTuple row, col;
                FcnActiong.FindErModel(Image1,m_ErModelParam, out row,out col);
                HOperatorSet.DispCross(hWindowControl3.HalconWindow, row, col, 60, 0);

            }
            catch
            {
                ShowMsg("请采集图片");
            }
        }

        private void buttonX4_Click(object sender, EventArgs e)
        {
            try
            {
                Pcs.Holeparammodel.score = Convert.ToDouble(txb_score.Text);
                Pcs.Holeparammodel.numlevel = Convert.ToDouble(txb_numlevel.Text);
                Pcs.Holeparammodel.minangle = Convert.ToDouble(txb_minangle.Text);
                Pcs.Holeparammodel.maxangle = Convert.ToDouble(txb_maxangle.Text);
                Pcs.Holeparammodel.angelstep = Convert.ToDouble(txb_anglestep.Text);
                Pcs.Holeparammodel.mincontrast = Convert.ToDouble(txb_mincontrast.Text);
                Pcs.Holeparammodel.highcontrast = Convert.ToDouble(txb_lowcontrast.Text);
                Pcs.Holeparammodel.lowcontrast = Convert.ToDouble(txb_highcontrast.Text);
                Pcs.Holeparammodel.minsize = Convert.ToDouble(txb_minsize.Text);

                Pcs.Holeparammodel.Modelparam.Cmx = Convert.ToDouble(txb_modelx.Text);
                Pcs.Holeparammodel.Modelparam.Cmy = Convert.ToDouble(txb_modely.Text);
                Pcs.Holeparammodel.Modelparam.Cma = Convert.ToDouble(txb_modela.Text);

                Pcs.Holeparammodel.Modelparam.ModelId = ModelId;

                ConfigIni.WriteIniKey("右相机", "得分", Pcs.Holeparammodel.score.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "金字塔", Pcs.Holeparammodel.numlevel.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "最小角度", Pcs.Holeparammodel.minangle.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "最大角度", Pcs.Holeparammodel.maxangle.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "角度步进", Pcs.Holeparammodel.angelstep.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "最小对比度", Pcs.Holeparammodel.mincontrast.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "对比度高", Pcs.Holeparammodel.highcontrast.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "对比度低", Pcs.Holeparammodel.lowcontrast.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "最小尺寸", Pcs.Holeparammodel.minsize.ToString(), Pcs.FilePath);

                ConfigIni.WriteIniKey("右相机", "坐标x", Pcs.Holeparammodel.Modelparam.Cmx.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "坐标y", Pcs.Holeparammodel.Modelparam.Cmy.ToString(), Pcs.FilePath);
                ConfigIni.WriteIniKey("右相机", "坐标a", Pcs.Holeparammodel.Modelparam.Cma.ToString(), Pcs.FilePath);
                HOperatorSet.WriteShapeModel(Pcs.Holeparammodel.Modelparam.ModelId, Application.StartupPath + "\\config\\" + "shape_model.shm");
                HOperatorSet.WriteRegion(Pcs.Holeparammodel.Modelparam.Cregion, Application.StartupPath + "\\config\\" + "combinereginon.reg");

            }
            catch
            { }
        }
        HTuple ModelId;
        private void buttonX5_Click(object sender, EventArgs e)
        {
           try
           {               
                HTuple hv_Row,hv_Column,hv_Angle;
                HoleParamModel HoleParamModel = new HoleParamModel();
                HoleParamModel.score = Convert.ToDouble(txb_score.Text);
                HoleParamModel.numlevel = Convert.ToDouble(txb_numlevel.Text);
                HoleParamModel.minangle = Convert.ToDouble(txb_minangle.Text);
                HoleParamModel.maxangle = Convert.ToDouble(txb_maxangle.Text);
                HoleParamModel.angelstep = Convert.ToDouble(txb_anglestep.Text);
                HoleParamModel.mincontrast = Convert.ToDouble(txb_mincontrast.Text);
                HoleParamModel.highcontrast = Convert.ToDouble(txb_lowcontrast.Text);
                HoleParamModel.lowcontrast = Convert.ToDouble(txb_highcontrast.Text);
                HoleParamModel.minsize = Convert.ToDouble(txb_minsize.Text);
                HoleParamModel.Modelparam.Cregion = Pcs.Holeparammodel.Modelparam.Cregion;
                
             int issuss=   FcnActiong.CreateHoleModel(Image2, HoleParamModel.Modelparam.Cregion, HoleParamModel, out ModelId);
                if (issuss == 1)
                {
                    bool sussok = FcnActiong.FindHoleModel(Image2, hWindowControl3.HalconWindow, HoleParamModel, ModelId, out hv_Row, out hv_Column, out hv_Angle);

                    Pcs.Holeparammodel.Modelparam.ModelId = ModelId;
                    txb_modelx.Text = hv_Row.D.ToString("0.000");
                    txb_modely.Text = hv_Column.D.ToString("0.000");
                    txb_modela.Text = hv_Angle.D.ToString("0.000");
                }
                else
                {
                    ShowMsg("模板创建失败");
                }
            }
            catch
            {
                ShowMsg("请采集图片，创建检测区域，并输入参数");
            }
        }

        private void buttonX1_MouseDown(object sender, MouseEventArgs e)
        {
            hWindowControl3.ContextMenuStrip = null;
            HOperatorSet.SetCheck("~give_error");
            HOperatorSet.SetSystem("clip_region", "false");
            HOperatorSet.SetColor(hWindowControl3.HalconWindow, "red");
            HOperatorSet.SetDraw(hWindowControl3.HalconWindow, "margin");
            HTuple row1, col1, row2, col2;
            HObject region;
            HOperatorSet.GenEmptyObj(out region);
            try
            {
                HOperatorSet.DrawRectangle1(hWindowControl3.HalconWindow, out row1, out col1, out row2, out col2);
                //Pcs.Camparm[0].ChosedRegion.Dispose();
                region.Dispose();
                HOperatorSet.GenRectangle1(out region, row1, col1, row2, col2);

                Pcs.m_ErModelParam.hmregion = region;
                //if (comboBox9.SelectedIndex == 0)
                {
                    //Pcs.m_ErModelParam.radius1 = radius;
                    //Pcs.m_ErModelParam.hmregion1 = region;

                }
                //else if (comboBox9.SelectedIndex == 1)
                //{
                //    //Pcs.m_ErModelParam.radius2 = radius;
                //    Pcs.m_ErModelParam.hmregion2 = region;                
                //}
                             
            }
            catch
            {
                Pcs.m_ErModelParam.hmregion = null;
            }
            hWindowControl3.ContextMenuStrip = contextMenuStrip4;
        }

        private void button3_MouseDown(object sender, MouseEventArgs e)
        {
            hWindowControl3.ContextMenuStrip = null;
            HOperatorSet.SetCheck("~give_error");
            HOperatorSet.SetSystem("clip_region", "false");
            HOperatorSet.SetColor(hWindowControl3.HalconWindow, "red");
            HOperatorSet.SetDraw(hWindowControl3.HalconWindow, "margin");
            HTuple row, col, angle, length1,length2;
            HObject region;

            HOperatorSet.GenEmptyObj(out region);
            try
            {
                HOperatorSet.DrawRectangle2(hWindowControl3.HalconWindow, out row, out col, out angle,out length1,out length2);
                // Pcs.Camparm[0].ChosedRegion.Dispose();
                region.Dispose();
                HOperatorSet.GenRectangle2(out region, row, col, angle, length1, length2);
                
              if (comboBox1.SelectedIndex == 0)
              {
                 //Pcs.Holeparammodel.ampm.r2_1x= row;
                 //Pcs.Holeparammodel.ampm.r2_1y= col;
                 //Pcs.Holeparammodel.ampm.r2_1a= angle;
                 //Pcs.Holeparammodel.ampm.r2_1l1= length1;
                 //Pcs.Holeparammodel.ampm.r2_1l2= length2;

                 txb_L1row.Text = row.D.ToString("0.000");
                 txb_L1col.Text = col.D.ToString("0.000");
                 txb_L1angle.Text = angle.D.ToString("0.000");
                 txb_L1length1.Text = length1.D.ToString("0.000");
                 txb_L1length2.Text = length2.D.ToString("0.000");
                  
              }
              else if (comboBox1.SelectedIndex == 1)
              {
                 //Pcs.Holeparammodel.ampm.r2_1x = row;
                 //Pcs.Holeparammodel.ampm.r2_2y = col;
                 //Pcs.Holeparammodel.ampm.r2_2a = angle;
                 //Pcs.Holeparammodel.ampm.r2_2l1 = length1;
                 //Pcs.Holeparammodel.ampm.r2_2l2 = length2;

                 txb_L2row.Text = row.D.ToString("0.000");
                 txb_L2col.Text = col.D.ToString("0.000");
                 txb_L2angle.Text = angle.D.ToString("0.000");
                 txb_L2length1.Text = length1.D.ToString("0.000");
                 txb_L2length2.Text = length2.D.ToString("0.000");
                }               
             
            }
            catch
            {
               
               
            }
            hWindowControl3.ContextMenuStrip = contextMenuStrip4;
        }
        int Camchose = 0;
        private void buttonX8_Click(object sender, EventArgs e)
        {
            Camchose = 2;
            if (m_bGrabbing2 != true)
            {
                m_pMyCamera2.MV_CC_StopGrabbing_NET();
            }
            Pcs.CamRunState = 2;
            m_bGrabbing2 = true;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerSource", 7);
            int nRet;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerMode", 1);
            nRet = m_pMyCamera2.MV_CC_StartGrabbing_NET();
            // ch: 触发命令 || en: Trigger command
            nRet = m_pMyCamera2.MV_CC_SetCommandValue_NET("TriggerSoftware");

            // 等待一小段时间确保图像采集完成
            Thread.Sleep(500);

            // 保存图片到C盘images文件夹
            SaveImageToLocal(Image2, "Camera2");
        }

        private void buttonX7_Click(object sender, EventArgs e)
        {
            Camchose = 1;
            if (m_bGrabbing1 != true)
            {
                m_pMyCamera1.MV_CC_StopGrabbing_NET();
            }
            Pcs.CamRunState = 2;
            m_bGrabbing1 = true;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerSource", 7);
            int nRet;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerMode", 1);
            nRet = m_pMyCamera1.MV_CC_StartGrabbing_NET();
            // ch: 触发命令 || en: Trigger command
            nRet = m_pMyCamera1.MV_CC_SetCommandValue_NET("TriggerSoftware");

            // 等待一小段时间确保图像采集完成
            Thread.Sleep(500);

            // 保存图片到C盘images文件夹
            SaveImageToLocal(Image1, "Camera1");
        }

        /// <summary>
        /// 保存图片到C盘images文件夹
        /// </summary>
        /// <param name="image">要保存的图片</param>
        /// <param name="cameraName">相机名称，用于文件命名</param>
        private void SaveImageToLocal(HObject image, string cameraName)
        {
            try
            {
                // 检查图片是否有效
                if (image == null || !image.IsInitialized())
                {
                    ShowMsg("图片无效，无法保存");
                    return;
                }

                // 创建C盘images文件夹
                string saveDirectory = @"C:\images";
                if (!Directory.Exists(saveDirectory))
                {
                    Directory.CreateDirectory(saveDirectory);
                }

                // 生成文件名：相机名称_时间戳.bmp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                string fileName = $"{cameraName}_{timestamp}.bmp";
                string fullPath = Path.Combine(saveDirectory, fileName);

                // 使用HalconDotNet保存图片为BMP格式
                HOperatorSet.WriteImage(image, "bmp", 0, fullPath);

                ShowMsg($"图片已保存到: {fullPath}");
            }
            catch (Exception ex)
            {
                ShowMsg($"保存图片失败: {ex.Message}");
            }
        }

        private void 图像还原ToolStripMenuItem2_Click(object sender, EventArgs e)
        {
            try
            {
                HTuple hh, hw;
                if (Image1 != null)
                {
                    HOperatorSet.GetImageSize(Image1, out hw, out hh);
                    HOperatorSet.SetPart(hWindowControl3.HalconWindow, 0, 0, hh, hw);
                    if (Camchose == 1)
                    {
                        HOperatorSet.DispObj(Image1, hWindowControl3.HalconWindow);
                    }
                    else if (Camchose == 2)
                    {
                        HOperatorSet.DispObj(Image2, hWindowControl3.HalconWindow);
                    }
                }
            }
            catch
            { }
        }
           
        private void button9_Click(object sender, EventArgs e)
        {
            Pcs.IPAdress = textBox22.Text;
            ConfigIni.WriteIniKey("地址", "IP", Pcs.IPAdress, Pcs.FilePath);
        }
        
        private void 触发流程ToolStripMenuItem_Click(object sender, EventArgs e)
        {
           Pcs.CamRunState = 3;
            if (m_bGrabbing1 != true)
            {
                m_pMyCamera1.MV_CC_StopGrabbing_NET();
            }
            m_bGrabbing1 = true;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerSource", 7);
            int nRet;
            m_pMyCamera1.MV_CC_SetEnumValue_NET("TriggerMode", 1);
            nRet = m_pMyCamera1.MV_CC_StartGrabbing_NET();
            // ch: 触发命令 || en: Trigger command
            nRet = m_pMyCamera1.MV_CC_SetCommandValue_NET("TriggerSoftware");
            Thread.Sleep(250);
            
        }
        
        private void StationOneAction()
        {
           
            HTuple x = null, y = null;
            float qx, qy;
            if (Pcs.IsStartSoft == true)
            {
                try
                {
                    //HTuple row, col;
                    //bool bm = FcnActiong.FindErModel(Image1, Pcs.m_ErModelParam, out row, out col);

                    if (cMat.Empty()) { ShowMsg("图像获取失败"); return; }

                   
                    bool result = CalcUtils.Check(cMat);

                    //ShowMsg("result：" + result);

                    if (result)
                    {
                        //HOperatorSet.DispCross(hWindowControl1.HalconWindow, row, col, 60, 0);
                        ShowMsg("耳朵位置正常，请继续");
                        FcnActiong.disp_message(hWindowControl1.HalconWindow, "耳朵位置正常", "window", 0, 0, "green", "true");
                        //lock (oj1)
                        //{
                        //    result1.Add(1);
                        //}
                        m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
                    }
                    else
                    {
                        FcnActiong.disp_message(hWindowControl1.HalconWindow, "耳朵位置异常", "window", 0, 0, "red", "true");
                        ShowMsg("耳朵位置异常，请继续");
                        //lock (oj1)
                        //{
                        //    result1.Add(0);
                        //}
                        //m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
                    }
                }
                catch (Exception EX)
                {
                    //m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
                    //lock (oj1)
                    //{
                    //    result1.Add(0);
                    //}
                    FcnActiong.disp_message(hWindowControl1.HalconWindow, "耳朵位置异常", "window", 0, 0, "red", "true");
                    ShowMsg("耳朵位置异常："+ EX.Message);
                    throw;
                }
                Thread.Sleep(100);
                Image1.Dispose();
                cMat.Dispose();
                //OneAction = 0;
                //OneIn = 0;
                GC.Collect();
            }
        }

        private void hWindowControl2_HMouseWheel(object sender, HMouseEventArgs e)
        {
            try
            {
                HTuple mode = e.Delta;
                int button_state;
                double mouse_post_row, mouse_pose_col;
                HOperatorSet.SetCheck("~give_error");
                HOperatorSet.SetSystem("clip_region", "false");
                hWindowControl2.HalconWindow.GetMpositionSubPix(out mouse_post_row, out mouse_pose_col, out button_state);
                IZHw.DispImageZoom(Image2, hWindowControl2, mode, mouse_post_row, mouse_pose_col);
            }
            catch
            {
                //MessageBox.Show(ex.ToString());

            }
        }

        private void 图像还原ToolStripMenuItem3_Click(object sender, EventArgs e)
        {
            try
            {
                HTuple hh, hw;
                if (Image2 != null)
                {
                    HOperatorSet.GetImageSize(Image2, out hw, out hh);
                    HOperatorSet.SetPart(hWindowControl2.HalconWindow, 0, 0, hh, hw);
                    HOperatorSet.DispObj(Image2, hWindowControl2.HalconWindow);
                }
            }
            catch
            { }
        }

        private void 连续采集ToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            Pcs.CamRunState = 0;
            if (m_bGrabbing2 != true)
            {
                m_pMyCamera2.MV_CC_StopGrabbing_NET();
            }
            m_bGrabbing2 = true;
            int nRet;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerMode", 0);
            nRet = m_pMyCamera2.MV_CC_StartGrabbing_NET();
        }

        private void 单次采集ToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            Pcs.CamRunState = 0;
            if (m_bGrabbing2 != true)
            {
                m_pMyCamera2.MV_CC_StopGrabbing_NET();
            }
            m_bGrabbing2 = true;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerSource", 7);
            int nRet;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerMode", 1);
            nRet = m_pMyCamera2.MV_CC_StartGrabbing_NET();
            // ch: 触发命令 || en: Trigger command
            nRet = m_pMyCamera2.MV_CC_SetCommandValue_NET("TriggerSoftware");
        }

        private void 停止采集ToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            int nRet = -1;
            // ch:停止抓图 || en:Stop grab image
            nRet = m_pMyCamera2.MV_CC_StopGrabbing_NET();

            m_bGrabbing2 = false;
        }

        private void 触发流程ToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            Pcs.CamRunState = 3;
            if (m_bGrabbing2 != true)
            {
                m_pMyCamera2.MV_CC_StopGrabbing_NET();
            }
            m_bGrabbing2 = true;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerSource", 7);
            int nRet;
            m_pMyCamera2.MV_CC_SetEnumValue_NET("TriggerMode", 1);
            nRet = m_pMyCamera2.MV_CC_StartGrabbing_NET();
            // ch: 触发命令 || en: Trigger command
            nRet = m_pMyCamera2.MV_CC_SetCommandValue_NET("TriggerSoftware");
            Thread.Sleep(250);
           
        }
       private void StationTwoAction()
        {
           
            HTuple x = null, y = null;
            float qx, qy;
            if (Pcs.IsStartSoft == true)
            {
                try
                {
                    HTuple angle = null;

                    bool sussok = FcnActiong.CalcOfLLAngle(Image2, Pcs.Holeparammodel, hWindowControl1.HalconWindow, out angle);
                    if (sussok)
                    {
                        double Aa = angle.TupleSelect(0).D;
                        string Angle = angle.TupleSelect(0).D.ToString("0.000");
                        if (Math.Abs(Aa) <= Pcs.JugleAngle)
                        {
                            FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测结果" + Angle, "window", 0, 0, "green", "true");
                            lock (oj1)
                            {
                                result2.Add(1);
                            }
                        }
                        else
                        {
                            FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测过大" + Angle, "window", 0, 0, "red", "true");
                            lock (oj1)
                            {
                                result2.Add(0);
                            }
                        }
                    }
                    else
                    {
                        lock (oj1)
                        {
                            result2.Add(0);
                        }
                        FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测失败", "window", 0, 0, "red", "true");
                        ShowMsg("角度检测失败，请检查参数");
                    }
                }
                catch
                {
                    lock (oj1)
                    {
                        result2.Add(0);
                    }
                    FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测失败", "window", 0, 0, "red", "true");
                    ShowMsg("角度检测失败，请检查参数");
                }

                Thread.Sleep(100);
                Image2.Dispose();
                //TwoAction = 0;
                //TwoIn = 0;
                GC.Collect();
            }
        }
      
        #region 内存回收
        [DllImport("kernel32.dll", EntryPoint = "SetProcessWorkingSetSize")]
        public static extern int SetProcessWorkingSetSize(IntPtr process, int minSize, int maxSize);
        /// <summary>
        /// 释放内存
        /// </summary>
        public static void ClearMemory()
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            if (Environment.OSVersion.Platform == PlatformID.Win32NT)
            {
                SetProcessWorkingSetSize(System.Diagnostics.Process.GetCurrentProcess().Handle, -1, -1);
            }
        }
        #endregion
        int m = 0;
        private void timer1_Tick(object sender, EventArgs e)
        {
            Application.DoEvents();
            ClearMemory();
        }
        
        string path;
        private void button23_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog file = new OpenFileDialog();
                //file.Filter = "|*.bmp;*.png;*.jpg";
                file.Filter = "BMP文件(*.bmp)|*bmp|PNG文件(*.png)|*png|JPG文件(*.jpg)|*jpg|所有文件(*.*)|*.*||";
                if (file.ShowDialog() == DialogResult.OK)
                {
                   
                    HOperatorSet.GenEmptyObj(out Image1);
                    path = file.FileName;
                    HTuple hh, hw;
                    HOperatorSet.ReadImage(out Image1, path);
                    HOperatorSet.GetImageSize(Image1, out hh, out hw);
                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hw, hh);
                    HOperatorSet.SetPart(hWindowControl3.HalconWindow, 0, 0, hw, hh);
                    HOperatorSet.DispObj(Image1, hWindowControl1.HalconWindow);
                    HOperatorSet.DispObj(Image1, hWindowControl3.HalconWindow);
                                        
                }
            }
            catch (Exception EX)
            {
                throw;
            }
        }

        public void UpForm()
        {
            if (Pcs.Level == 0)
            {
                /////图像界面
                hWindowControl1.Enabled = false;
                hWindowControl2.Enabled = false;

                tabControl2.Enabled = false;

                comboBox3.Enabled = false;
                button27.Enabled = false;
                button22.Enabled = false;
                button26.Enabled = false;
                button30.Enabled = false;
            }
            else if (Pcs.Level == 1)
            {
                /////图像界面
                hWindowControl1.Enabled = true;
                hWindowControl2.Enabled = true;

                tabControl2.Enabled = true;

                comboBox3.Enabled = true;
                button27.Enabled = true;
                button22.Enabled = true;
                button26.Enabled = true;
                button30.Enabled = true;
            }
           

        }

        private void button24_Click(object sender, EventArgs e)
        {
            if (comboBox2.SelectedIndex == 1)
            {
                if (textBox10.Text == "668")
                {
                    ShowMsg("工程师登录");
                    Pcs.Level = 1;
                    UpForm();
                }
                else
                {
                    ShowMsg("工程师退出");
                    Pcs.Level = 0;
                    UpForm();
                }
            }           
        }

        private void button23_Click_1(object sender, EventArgs e)
        {
            Pcs.Level = 0;
            UpForm();
            comboBox2.SelectedIndex = -1;
            textBox10.Text = null;
        }
        
        private void button28_Click(object sender, EventArgs e)
        {
            Pcs.m_ErModelParam.hmregion = null;
            //if (comboBox9.SelectedIndex == 0)
            //{
            //    Pcs.m_ErModelParam.hmregion1 = null;
            //}
            //else if (comboBox9.SelectedIndex == 1)
            //{
            //    Pcs.m_ErModelParam.hmregion2 = null;
            //}
        }

        private void button29_Click(object sender, EventArgs e)
        {
            Pcs.Holeparammodel.Modelparam.Cregion = null;
        }

        private void button4_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog file = new OpenFileDialog();
                //file.Filter = "|*.bmp;*.png;*.jpg";
                file.Filter = "BMP文件(*.bmp)|*bmp|PNG文件(*.png)|*png|JPG文件(*.jpg)|*jpg|所有文件(*.*)|*.*||";
                if (file.ShowDialog() == DialogResult.OK)
                {
                    HOperatorSet.GenEmptyObj(out Image1);
                    path = file.FileName;
                    HTuple hh, hw;
                    HOperatorSet.ReadImage(out Image1, path);
                    HOperatorSet.GetImageSize(Image1, out hh, out hw);                   
                    HOperatorSet.SetPart(hWindowControl3.HalconWindow, 0, 0, hw, hh);                    
                    HOperatorSet.DispObj(Image1, hWindowControl3.HalconWindow);

                }
            }
            catch (Exception EX)
            {
                throw;
            }
        }

        private void button6_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog file = new OpenFileDialog();
                //file.Filter = "|*.bmp;*.png;*.jpg";
                file.Filter = "BMP文件(*.bmp)|*bmp|PNG文件(*.png)|*png|JPG文件(*.jpg)|*jpg|所有文件(*.*)|*.*||";
                if (file.ShowDialog() == DialogResult.OK)
                {

                    HOperatorSet.GenEmptyObj(out Image2);
                    path = file.FileName;
                    HTuple hh, hw;
                    HOperatorSet.ReadImage(out Image2, path);
                    HOperatorSet.GetImageSize(Image2, out hh, out hw);
                    HOperatorSet.SetPart(hWindowControl3.HalconWindow, 0, 0, hw, hh);
                    HOperatorSet.DispObj(Image2, hWindowControl3.HalconWindow);

                }
            }
            catch (Exception EX)
            {
                throw;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (comboBox1.SelectedIndex == 0)
            {
                txb_L1row.Text = "0.0";
                txb_L1col.Text = "0.0";
                txb_L1angle.Text = "0.0";
                txb_L1length1.Text = "0.0";
                txb_L1length2.Text = "0.0";
            }
            else if (comboBox1.SelectedIndex == 1)
            {
                txb_L2row.Text = "0.0";
                txb_L2col.Text = "0.0";
                txb_L2angle.Text = "0.0";
                txb_L2length1.Text = "0.0";
                txb_L2length2.Text = "0.0";
            }
        }

        private void button19_MouseDown(object sender, MouseEventArgs e)
        {
            hWindowControl3.ContextMenuStrip = null;
            HOperatorSet.SetCheck("~give_error");
            HOperatorSet.SetSystem("clip_region", "false");
            HOperatorSet.SetColor(hWindowControl3.HalconWindow, "red");
            HOperatorSet.SetDraw(hWindowControl3.HalconWindow, "margin");
            HTuple row1, col1, row2, col2;
            HObject region;

            HOperatorSet.GenEmptyObj(out region);
            try
            {
                HOperatorSet.DrawRectangle1(hWindowControl3.HalconWindow, out row1, out col1, out row2, out col2);
                // Pcs.Camparm[0].ChosedRegion.Dispose();
                region.Dispose();
                HOperatorSet.GenRectangle1(out region, row1, col1, row2, col2);

                if (Pcs.Holeparammodel.Modelparam.Cregion == null)
                {
                    Pcs.Holeparammodel.Modelparam.Cregion = region;
                }
                else
                {
                    if (comboBox10.SelectedIndex == 0)
                    {
                        HOperatorSet.Union2(Pcs.Holeparammodel.Modelparam.Cregion, region, out Pcs.Holeparammodel.Modelparam.Cregion);


                    }
                    else if (comboBox10.SelectedIndex == 1)
                    {
                        HOperatorSet.Difference(Pcs.Holeparammodel.Modelparam.Cregion, region, out Pcs.Holeparammodel.Modelparam.Cregion);
                    }
                }

            }
            catch
            {
                Pcs.Holeparammodel.Modelparam.Cregion = region;

            }
            hWindowControl3.ContextMenuStrip = contextMenuStrip4;
        }

        private void button2_Click(object sender, EventArgs e)
        {           
            HoleParamModel HPM = new HoleParamModel();
            HPM.score = Convert.ToDouble(txb_score.Text);
            HPM.numlevel = Convert.ToDouble(txb_numlevel.Text);
            HPM.minangle = Convert.ToDouble(txb_minangle.Text);
            HPM.maxangle = Convert.ToDouble(txb_maxangle.Text);
            HPM.angelstep = Convert.ToDouble(txb_anglestep.Text);
            HPM.mincontrast = Convert.ToDouble(txb_mincontrast.Text);
            HPM.highcontrast = Convert.ToDouble(txb_lowcontrast.Text);
            HPM.lowcontrast = Convert.ToDouble(txb_highcontrast.Text);
            HPM.minsize = Convert.ToDouble(txb_minsize.Text);
            HPM.Modelparam.Cregion = Pcs.Holeparammodel.Modelparam.Cregion;
            HPM.Modelparam.ModelId = Pcs.Holeparammodel.Modelparam.ModelId;

            HPM.Modelparam.Cmx = Convert.ToDouble(txb_modelx.Text);
            HPM.Modelparam.Cmy = Convert.ToDouble(txb_modely.Text);
            HPM.Modelparam.Cma = Convert.ToDouble(txb_modela.Text);

            HPM.ampm.r2_1x  = Convert.ToDouble(txb_L1row.Text);
            HPM.ampm.r2_1y  = Convert.ToDouble(txb_L1col.Text);
            HPM.ampm.r2_1a  = Convert.ToDouble(txb_L1angle.Text);
            HPM.ampm.r2_1l1 = Convert.ToDouble(txb_L1length1.Text);
            HPM.ampm.r2_1l2 = Convert.ToDouble(txb_L1length2.Text);


            HPM.ampm.r2_2x = Convert.ToDouble(txb_L2row.Text);
            HPM.ampm.r2_2y = Convert.ToDouble(txb_L2col.Text);
            HPM.ampm.r2_2a = Convert.ToDouble(txb_L2angle.Text);
            HPM.ampm.r2_2l1 = Convert.ToDouble(txb_L2length1.Text);
            HPM.ampm.r2_2l2 = Convert.ToDouble(txb_L2length2.Text);

            HPM.ampm.x1 = Convert.ToDouble(txb_mox1.Text);
            HPM.ampm.y1 = Convert.ToDouble(txb_moy1.Text);
            HPM.ampm.x2 = Convert.ToDouble(txb_mox2.Text);
            HPM.ampm.y2 = Convert.ToDouble(txb_moy2.Text);
            
            HTuple angle=null;

            bool sussok = FcnActiong.CalcOfLLAngle(Image2,HPM, hWindowControl3.HalconWindow, out angle);
            if (sussok)
            {
                string Angle = angle.TupleSelect(0).D.ToString("0.000");
                FcnActiong.disp_message(hWindowControl3.HalconWindow, Angle, "window", 0, 0, "green", "true");
            }
            else
            {
                ShowMsg("角度检测失败，请检查参数");
            }
        }

        private void button5_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog file = new OpenFileDialog();
                //file.Filter = "|*.bmp;*.png;*.jpg";
                file.Filter = "BMP文件(*.bmp)|*bmp|PNG文件(*.png)|*png|JPG文件(*.jpg)|*jpg|所有文件(*.*)|*.*||";
                if (file.ShowDialog() == DialogResult.OK)
                {
                    HOperatorSet.GenEmptyObj(out Image2);
                    path = file.FileName;
                    HTuple hh, hw;
                    HOperatorSet.ReadImage(out Image2, path);
                    HOperatorSet.GetImageSize(Image2, out hh, out hw);
                    HOperatorSet.SetPart(hWindowControl3.HalconWindow, 0, 0, hw, hh);
                    HOperatorSet.DispObj(Image2, hWindowControl3.HalconWindow);

                }
            }
            catch (Exception EX)
            {
                throw;
            }
        }

        private void button7_Click(object sender, EventArgs e)
        {
            Pcs.Holeparammodel.ampm.r2_1x = Convert.ToDouble(txb_L1row.Text);
            Pcs.Holeparammodel.ampm.r2_1y = Convert.ToDouble(txb_L1col.Text);
            Pcs.Holeparammodel.ampm.r2_1a = Convert.ToDouble(txb_L1angle.Text);
            Pcs.Holeparammodel.ampm.r2_1l1 = Convert.ToDouble(txb_L1length1.Text);
            Pcs.Holeparammodel.ampm.r2_1l2 = Convert.ToDouble(txb_L1length2.Text);

            Pcs.Holeparammodel.ampm.r2_2x = Convert.ToDouble(txb_L2row.Text);
            Pcs.Holeparammodel.ampm.r2_2y = Convert.ToDouble(txb_L2col.Text);
            Pcs.Holeparammodel.ampm.r2_2a = Convert.ToDouble(txb_L2angle.Text);
            Pcs.Holeparammodel.ampm.r2_2l1 = Convert.ToDouble(txb_L2length1.Text);
            Pcs.Holeparammodel.ampm.r2_2l2 = Convert.ToDouble(txb_L2length2.Text);

            Pcs.Holeparammodel.ampm.x1 = Convert.ToDouble(txb_mox1.Text);
            Pcs.Holeparammodel.ampm.y1 = Convert.ToDouble(txb_moy1.Text);
            Pcs.Holeparammodel.ampm.x2 = Convert.ToDouble(txb_mox2.Text);
            Pcs.Holeparammodel.ampm.y2 = Convert.ToDouble(txb_moy2.Text);

            Pcs.Holeparammodel.score = Convert.ToDouble(txb_score.Text);
            Pcs.Holeparammodel.numlevel = Convert.ToDouble(txb_numlevel.Text);
            Pcs.Holeparammodel.minangle = Convert.ToDouble(txb_minangle.Text);
            Pcs.Holeparammodel.maxangle = Convert.ToDouble(txb_maxangle.Text);
            Pcs.Holeparammodel.angelstep = Convert.ToDouble(txb_anglestep.Text);
            Pcs.Holeparammodel.mincontrast = Convert.ToDouble(txb_mincontrast.Text);
            Pcs.Holeparammodel.highcontrast = Convert.ToDouble(txb_lowcontrast.Text);
            Pcs.Holeparammodel.lowcontrast = Convert.ToDouble(txb_highcontrast.Text);
            Pcs.Holeparammodel.minsize = Convert.ToDouble(txb_minsize.Text);

            Pcs.Holeparammodel.Modelparam.Cmx = Convert.ToDouble(txb_modelx.Text);
            Pcs.Holeparammodel.Modelparam.Cmy = Convert.ToDouble(txb_modely.Text);
            Pcs.Holeparammodel.Modelparam.Cma = Convert.ToDouble(txb_modela.Text);

            if (ModelId != null)
            {
                Pcs.Holeparammodel.Modelparam.ModelId = ModelId;
            }

            camright cmr = new camright();
            cmr.productcode = Pcs.ProductCode;
            cmr.r2_1x = Convert.ToDouble(txb_L1row.Text);
            cmr.r2_1y = Convert.ToDouble(txb_L1col.Text);
            cmr.r2_1a = Convert.ToDouble(txb_L1angle.Text);
            cmr.r2_1l1 = Convert.ToDouble(txb_L1length1.Text);
            cmr.r2_1l2 = Convert.ToDouble(txb_L1length2.Text);

            cmr.r2_2x = Convert.ToDouble(txb_L2row.Text);
            cmr.r2_2y = Convert.ToDouble(txb_L2col.Text);
            cmr.r2_2a = Convert.ToDouble(txb_L2angle.Text);
            cmr.r2_2l1 = Convert.ToDouble(txb_L2length1.Text);
            cmr.r2_2l2 = Convert.ToDouble(txb_L2length2.Text);

            cmr.x1 = Convert.ToDouble(txb_mox1.Text);
            cmr.y1 = Convert.ToDouble(txb_moy1.Text);
            cmr.x2 = Convert.ToDouble(txb_mox2.Text);
            cmr.y2 = Convert.ToDouble(txb_moy2.Text);

            cmr.score = Convert.ToDouble(txb_score.Text);
            cmr.numlevel = Convert.ToDouble(txb_numlevel.Text);
            cmr.minangle = Convert.ToDouble(txb_minangle.Text);
            cmr.maxangle = Convert.ToDouble(txb_maxangle.Text);
            cmr.angelstep = Convert.ToDouble(txb_anglestep.Text);
            cmr.mincontrast = Convert.ToDouble(txb_mincontrast.Text);
            cmr.highcontrast = Convert.ToDouble(txb_lowcontrast.Text);
            cmr.lowcontrast = Convert.ToDouble(txb_highcontrast.Text);
            cmr.minsize = Convert.ToDouble(txb_minsize.Text);

            cmr.Cmx = Convert.ToDouble(txb_modelx.Text);
            cmr.Cmy = Convert.ToDouble(txb_modely.Text);
            cmr.Cma = Convert.ToDouble(txb_modela.Text);
            
            int IsUp = UpDateCamRight(cmr);
            if (IsUp == 0)
            {
                IsUp = WriteCamRightProcess(cmr);               
            }
            if (IsUp >= 1)
            {
                HOperatorSet.WriteShapeModel(Pcs.Holeparammodel.Modelparam.ModelId, Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".shm");
                HOperatorSet.WriteRegion(Pcs.Holeparammodel.Modelparam.Cregion, Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg");
                MessageBox.Show("右相机参数写入成功！");
            }
            else
            {
                MessageBox.Show("右相机参数写入失败！");
            }
        }

        int UpDateCamRight(camright cmr)
        {
            var sql = @"update camright set score="+cmr.score+ ",numlevel="+cmr.numlevel+ ",minangle="+cmr.minangle+ ",maxangle=" + cmr.maxangle+
               " ,angelstep="+cmr.angelstep+ ",mincontrast="+cmr.mincontrast+ ",highcontrast="+cmr.highcontrast+ ",lowcontrast="+cmr.lowcontrast+
               ",minsize="+cmr.minsize+ ",Cmx="+cmr.Cmx+ ",Cmy="+cmr.Cmy+ ",Cma="+cmr.Cma+ ",r2_1x="+cmr.r2_1x+ ",r2_1y="+cmr.r2_1y+ ",r2_1a="+cmr.r2_1a+
                ",r2_1l1=" + cmr.r2_1l1 + ",r2_1l2=" + cmr.r2_1l2 + ",r2_2x=" + cmr.r2_2x + ",r2_2y=" + cmr.r2_2y + ",r2_2a=" + cmr.r2_2a + ",r2_2l1=" + cmr.r2_2l1+
                ",r2_2l2=" + cmr.r2_2l2+ ",x1=" + cmr.x1 + ",y1=" + cmr.y1 + ",x2=" + cmr.x2 + ",y2=" + cmr.y2+ " WHERE productcode= '" + cmr.productcode + "'"; ;
            using (var con = DbHelper.GetDbConnection())
            {
                try
                {
                    var res = con.Execute(sql, cmr); //添加成功的话，返回值一定大于0
                    return res;
                }
                catch (Exception ex)
                {
                    return 0;
                }
            }
            

        }

        int selectRightParam( string pcname)
        {
            int num = 0;
            var sql = @"select productcode from camright where 1=1 ";

            var ps = new DynamicParameters();

            var where = " AND productcode ='" + pcname+"'";
            
            sql = sql + where ;
            using (var con = DbHelper.GetDbConnection())
            {
                try
                {
                    var res = con.QueryMultiple(sql);
                    if (!res.IsConsumed)
                    {
                        var vdefect = res.Read<string>().ToList();
                        num= vdefect.Count;
                    }
                }
                catch
                {
                    return 0;
                }                
            }
            return num;
        }

        int WriteCamRightProcess(camright cmr)
        {
            var sql = @"INSERT INTO camright ( 
                        productcode, 
                        score, 
                        numlevel, 
                        minangle, 
                        maxangle,
                        angelstep,
                        mincontrast,
                        highcontrast,
                        lowcontrast,
                        minsize,
                        Cmx,
                        Cmy,
                        Cma,
                        r2_1x,
                        r2_1y,
                        r2_1a,
                        r2_1l1,
                        r2_1l2,
                        r2_2x,
                        r2_2y,
                        r2_2a,
                        r2_2l1,
                        r2_2l2,
                        x1,
                        y1,
                        x2,
                        y2
                        )
                        VALUES
	                    (@productcode, 
                        @score, 
                        @numlevel, 
                        @minangle, 
                        @maxangle,
                        @angelstep,
                        @mincontrast,
                        @highcontrast,
                        @lowcontrast,
                        @minsize,
                        @Cmx,
                        @Cmy,
                        @Cma,
                        @r2_1x,
                        @r2_1y,
                        @r2_1a,
                        @r2_1l1,
                        @r2_1l2,
                        @r2_2x,
                        @r2_2y,
                        @r2_2a,
                        @r2_2l1,
                        @r2_2l2,
                        @x1,
                        @y1,
                        @x2,
                        @y2)";
            using (var con = DbHelper.GetDbConnection())
            {
                try
                {
                    var res = con.Execute(sql, cmr); //添加成功的话，返回值一定大于0
                    return res;
                }
                catch (Exception ex)
                {
                    return 0;
                }
            }


        }

        private void button8_Click(object sender, EventArgs e)
        {
            HTuple hv_Row, hv_Column, hv_Angle;
            HoleParamModel HoleParamModel = new HoleParamModel();
            HoleParamModel.score = Convert.ToDouble(txb_score.Text);
            HoleParamModel.numlevel = Convert.ToDouble(txb_numlevel.Text);
            HoleParamModel.minangle = Convert.ToDouble(txb_minangle.Text);
            HoleParamModel.maxangle = Convert.ToDouble(txb_maxangle.Text);
            HoleParamModel.angelstep = Convert.ToDouble(txb_anglestep.Text);
            HoleParamModel.mincontrast = Convert.ToDouble(txb_mincontrast.Text);
            HoleParamModel.highcontrast = Convert.ToDouble(txb_lowcontrast.Text);
            HoleParamModel.lowcontrast = Convert.ToDouble(txb_highcontrast.Text);
            HoleParamModel.minsize = Convert.ToDouble(txb_minsize.Text);
            HoleParamModel.Modelparam.Cregion = Pcs.Holeparammodel.Modelparam.Cregion;
            HoleParamModel.Modelparam.ModelId = Pcs.Holeparammodel.Modelparam.ModelId;
            bool sussok = FcnActiong.FindHoleModel(Image2, hWindowControl3.HalconWindow, HoleParamModel, HoleParamModel.Modelparam.ModelId, out hv_Row, out hv_Column, out hv_Angle);
            
        }

        private void 读图测试ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                HObject image;

                HOperatorSet.GenEmptyObj(out image);
                OpenFileDialog file = new OpenFileDialog();
                //file.Filter = "|*.bmp;*.png;*.jpg";
                bool result = false;
                file.Filter = "BMP文件(*.bmp)|*bmp|PNG文件(*.png)|*png|JPG文件(*.jpg)|*jpg|所有文件(*.*)|*.*||";
                if (file.ShowDialog() == DialogResult.OK)
                {
                    path = file.FileName;

                    Mat src = Cv2.ImRead(path,ImreadModes.Grayscale);
                    result = CalcUtils.Check(src);
                    HTuple hh, hw;
                    HOperatorSet.SetColor(hWindowControl1.HalconWindow, "red");
                    HOperatorSet.ReadImage(out image, path);
                    HOperatorSet.GetImageSize(image, out hh, out hw);
                    HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, hw, hh);
                    HOperatorSet.DispObj(image, hWindowControl1.HalconWindow);

                }           

           // HTuple row, col;
           //bool bm= FcnActiong.FindErModel(image, Pcs.m_ErModelParam, out row, out col);
                if (result)
                {
                    //HOperatorSet.DispCross(hWindowControl1.HalconWindow, row, col, 60, 0);
                    ShowMsg("耳朵位置正常");
                }
                else
                {
                    ShowMsg("耳朵位置异常");
                }

            }
            catch (Exception EX)
            {
                throw;
            }

        }

        private void 读图测试ToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            try
            {
                HObject image;

                HOperatorSet.GenEmptyObj(out image);
                OpenFileDialog file = new OpenFileDialog();
                //file.Filter = "|*.bmp;*.png;*.jpg";
                file.Filter = "BMP文件(*.bmp)|*bmp|PNG文件(*.png)|*png|JPG文件(*.jpg)|*jpg|所有文件(*.*)|*.*||";
                if (file.ShowDialog() == DialogResult.OK)
                {
                    path = file.FileName;
                    HTuple hh, hw;
                    HOperatorSet.SetColor(hWindowControl2.HalconWindow, "red");
                    HOperatorSet.ReadImage(out image, path);
                    HOperatorSet.GetImageSize(image, out hh, out hw);
                    HOperatorSet.SetPart(hWindowControl2.HalconWindow, 0, 0, hw, hh);
                    HOperatorSet.DispObj(image, hWindowControl2.HalconWindow);

                    HTuple angle = null;

                    bool sussok = FcnActiong.CalcOfLLAngle(image, Pcs.Holeparammodel, hWindowControl2.HalconWindow, out angle);
                    if (sussok)
                    {
                        double Aa = angle.TupleSelect(0).D;
                        string Angle = angle.TupleSelect(0).D.ToString("0.000");
                        if (Math.Abs(Aa) <= Pcs.JugleAngle)
                        {
                            FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测结果" + Angle, "window", 0, 0, "green", "true");
                         }
                        else
                        {
                            FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测过大"+ Angle, "window", 0, 0, "red", "true");
                        }
                    }
                    else
                    {
                        FcnActiong.disp_message(hWindowControl2.HalconWindow, "角度检测失败", "window", 0, 0, "red", "true");
                        ShowMsg("角度检测失败，请检查参数");
                    }


                }

               
            }
            catch (Exception EX)
            {
                throw;
            }
        }

        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            Pcs.JugleAngle = Convert.ToDouble(textBox4.Text);
            ConfigIni.WriteIniKey("系统参数", "角度判定", Pcs.JugleAngle.ToString(), Pcs.FilePath);
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            Pcs.CamNm1 =(textBox2.Text);
            ConfigIni.WriteIniKey("系统参数", "相机1", Pcs.CamNm1.ToString(), Pcs.FilePath);
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            Pcs.CamNm2 = (textBox3.Text);
            ConfigIni.WriteIniKey("系统参数", "相机2", Pcs.CamNm2.ToString(), Pcs.FilePath);
        }

        private void button12_Click(object sender, EventArgs e)
        {
          int net=  m_pMyCamera1.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
        }

        private void button13_Click(object sender, EventArgs e)
        {
            int net = m_pMyCamera2.MV_CC_SetCommandValue_NET("LineTriggerSoftware");
        }

        private void button22_Click(object sender, EventArgs e)
        {
            if (comboBox3.SelectedIndex >= 0)
            {
                DialogResult dr = MessageBox.Show("是否选择此产品？", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (dr == DialogResult.OK)
                {
                    Pcs.IsChoseProduct = true;
                    Pcs.ProductCode = comboBox3.Text;
                    Pcs.IsLoadCamProcess = GetCamProcess();
                }
            }
            else
            {
                MessageBox.Show("还没有选定当前生产产品名称", "控制警告", MessageBoxButtons.OKCancel,MessageBoxIcon.Warning);
            }

       }

        private void button27_Click(object sender, EventArgs e)
        {
            if (comboBox3.SelectedIndex >= 0)
            {
                DialogResult dr = MessageBox.Show("是否删除此产品？", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (dr == DialogResult.OK)
                {
                    Pcs.IsChoseProduct = false;
                    Pcs.ProductCode =string.Empty;
                    string tablename = "'" + comboBox3.Text + "'";
                    DeleteProcessData(tablename, "processname");
                    DeleteProcessData(tablename, "camright");
                    DeleteProcessData(tablename, "camleft");
                    if (File.Exists(Application.StartupPath + "\\config\\" + Pcs.ProductCode + "1.reg"))
                    {
                        File.Delete(Application.StartupPath + "\\config\\" + Pcs.ProductCode + "1.reg");
                        //HOperatorSet.DeleteFile(Application.StartupPath + "\\config\\" + Pcs.ProductCode + "1.reg");
                    }
                    if (File.Exists(Application.StartupPath + "\\config\\" + Pcs.ProductCode + "2.reg"))
                    {
                        File.Delete(Application.StartupPath + "\\config\\" + Pcs.ProductCode + "2.reg");
                        //HOperatorSet.DeleteFile(Application.StartupPath + "\\config\\" + Pcs.ProductCode + "2.reg");
                    }
                    if (File.Exists(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg"))
                    {
                        File.Delete(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg");
                        //HOperatorSet.DeleteFile(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".reg");
                    }
                    if (File.Exists(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".shm"))
                    {
                        File.Delete(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".shm");
                        //HOperatorSet.DeleteFile(Application.StartupPath + "\\config\\" + Pcs.ProductCode + ".shm");
                    }

                    comboBox3.Items.Remove(comboBox3.Text);
                    comboBox3.SelectedIndex = -1;


                    

                    ShowMsg("删除完成");
                }
            }
            else
            {
                MessageBox.Show("还没有选定当前生产产品名称", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
            }
        }
        void DeleteProcessData(string _pcode = "",string TableName="")
        {
            var sql = @"DELETE
                     FROM `"+
             TableName
             +
             "` WHERE productcode = ";

            var where = "";
            var ps = new DynamicParameters();

            if (_pcode != null)
            {
                where = _pcode;
                sql = sql + where;
                using (var con = DbHelper.GetDbConnection())
                {
                    var res = con.Query<string>(sql, ps);
                    
                }
            }
            else
            {
                MessageBox.Show("请慎重选择你需要删除的配方");
            }
        }

        private void button26_Click(object sender, EventArgs e)
        {
            if (Pcs.IsStartSoft == false)
            {
                DialogResult dr = MessageBox.Show("启动程序？", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (dr == DialogResult.OK)
                {                   
                    if (Pcs.IsLoadCamProcess == true)
                    {
                        Pcs.CamRunState = 3;
                        Pcs.IsStartSoft = true;
                        //ThreadRstIO = new Thread(ThreadAction);
                        //ThreadRstIO.IsBackground = true;
                        //ThreadRstIO.Start();

                        button22.Enabled = false;
                        button27.Enabled = false;
                        button30.Enabled = false;
                        comboBox3.Enabled = false;

                        button26.Text = "停止运行";
                        ShowMsg("运行启动成功");
                    }
                    else
                    {
                        Pcs.CamRunState = 0;
                        Pcs.IsLoadCamProcess = false;
                           Pcs.IsStartSoft = false;
                        //ThreadRstIO.Abort();
                        MessageBox.Show("运行启动失败", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                    }
                }
            }
            else
            {
                DialogResult dr = MessageBox.Show("停止程序？", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (dr == DialogResult.OK)
                {
                    button22.Enabled = true;
                    button27.Enabled = true;
                    button30.Enabled = true;
                    comboBox3.Enabled = true;
                    Pcs.IsStartSoft = false;
                    Pcs.IsStartSoft = false;
                    //ThreadRstIO.Abort();
                    button26.Text = "启动运行";

                    ShowMsg("运行状态关闭");
                }
            }
        }

        private void button30_Click(object sender, EventArgs e)
        {
            if (comboBox3.Text != string.Empty)
            {
                DialogResult dr = MessageBox.Show("添加此料号？", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (dr == DialogResult.OK)
                {

                    var res = SelectProcessNameData(comboBox3.Text);
                    if (res.Items.Count() == 0)
                    {
                        processname psn = new processname();
                        psn.productcode = comboBox3.Text;
                        WriteProcess(psn);
                        Pcs.IsChoseProduct = false;
                        Pcs.ProductCode = string.Empty;
                        comboBox3.Items.Add(comboBox3.Text);
                    }
                    else
                    {
                        MessageBox.Show("当前料号已经被添加", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                    }        
                }
            }
            else
            {
                MessageBox.Show("没有输入任何料号名称", "控制警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
            }

        }
        int WriteProcess(processname psn)
        {
            var sql = @"INSERT INTO processname ( productcode )
                                        VALUES
	                                (@productcode)";
            using (var con = DbHelper.GetDbConnection())
            {
                try
                {
                    var res = con.Execute(sql, psn); //添加成功的话，返回值一定大于0
                    return res;
                }
                catch (Exception ex)
                {
                    return 0;
                }
            }
        }

        PagedList<processname> SelectProcessNameData(string _productcode)
        {
            var sql = @"SELECT * from  processname where 1 = 1 ";//冒号前面加空格";

            var ps = new DynamicParameters();

            var where = " AND productcode = @productcode ";
            ps.Add("productcode", _productcode);
            sql = sql + where + " ORDER BY id desc";

            var pageres = new PagedList<processname>();
            using (var con = DbHelper.GetDbConnection())
            {
                var res = con.QueryMultiple(sql, ps);
                if (!res.IsConsumed)
                {
                    pageres.Items = res.Read<processname>().ToList();//查询出的数据

                }
            }
            return pageres;
        }

        private void button31_Click(object sender, EventArgs e)
        {
            SelectRoiFrm selectRoiFrm = new SelectRoiFrm();
            selectRoiFrm.ShowDialog();
        }
        private readonly static IniFileUtils iniFileUtil = new IniFileUtils(Environment.CurrentDirectory + "\\AppConfig.ini");
        private void okBtn_Click(object sender, EventArgs e)
        {
            if(!isOpen)
            {
                MessageBox.Show("相机未打开");
                return;
            }
            try
            {
                float.Parse(exposureTxt.Text);
                float.Parse(gainTxt.Text);
            }
            catch
            {
                MessageBox.Show("Please enter correct type!");
                return;
            }


            m_pMyCamera1.MV_CC_SetEnumValue_NET("ExposureAuto", 0);
            int nRet = m_pMyCamera1.MV_CC_SetFloatValue_NET("ExposureTime", float.Parse(exposureTxt.Text));
            if (nRet != MyCamera.MV_OK)
            {
                MessageBox.Show("Set Exposure Time Fail:"+nRet);
            }

            m_pMyCamera1.MV_CC_SetEnumValue_NET("GainAuto", 0);
            nRet = m_pMyCamera1.MV_CC_SetFloatValue_NET("Gain", float.Parse(gainTxt.Text));
            if (nRet != MyCamera.MV_OK)
            {
                MessageBox.Show("Set Gain Fail:" + nRet);
            }
            
            iniFileUtil.WriteString("param", "exposureTime", exposureTxt.Text);
            iniFileUtil.WriteString("param", "gain", gainTxt.Text);
            MessageBox.Show("设置成功");
        }
    }
}
